package services

import (
	"crypto/rand"
	"solve-go-api/internal/models"
	"strings"

	"gorm.io/gorm"
)

// AppService 应用服务
type AppService struct {
	db *gorm.DB
}

// NewAppService 创建应用服务
func NewAppService(db *gorm.DB) *AppService {
	return &AppService{db: db}
}

// CreateApp 创建应用
func (s *AppService) CreateApp(userID uint, name string) (*models.App, error) {
	// 生成AppID和AppSecret
	appID, err := s.generateAppID()
	if err != nil {
		return nil, err
	}

	appSecret, err := s.generateAppSecret()
	if err != nil {
		return nil, err
	}

	app := &models.App{
		UserID:    userID,
		Name:      name,
		AppID:     appID,
		AppSecret: appSecret,
		Status:    0, // 默认正常状态
	}

	err = s.db.Create(app).Error
	if err != nil {
		return nil, err
	}

	return app, nil
}

// GetAppByID 根据AppID获取应用
func (s *AppService) GetAppByID(appID string) (*models.App, error) {
	var app models.App
	err := s.db.Where("app_id = ?", appID).First(&app).Error
	if err != nil {
		return nil, err
	}
	return &app, nil
}

// GetAppsByUserID 获取用户的应用列表
func (s *AppService) GetAppsByUserID(userID uint) ([]models.App, error) {
	var apps []models.App
	err := s.db.Where("user_id = ?", userID).Find(&apps).Error
	return apps, err
}

// UpdateApp 更新应用
func (s *AppService) UpdateApp(id uint, updates map[string]interface{}) error {
	return s.db.Model(&models.App{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteApp 删除应用
func (s *AppService) DeleteApp(id uint) error {
	return s.db.Delete(&models.App{}, id).Error
}

// ResetAppSecret 重置应用密钥
func (s *AppService) ResetAppSecret(id uint) (string, error) {
	newSecret, err := s.generateAppSecret()
	if err != nil {
		return "", err
	}

	err = s.db.Model(&models.App{}).Where("id = ?", id).Update("app_secret", newSecret).Error
	if err != nil {
		return "", err
	}

	return newSecret, nil
}

// ValidateApp 验证应用凭证
func (s *AppService) ValidateApp(appID, appSecret string) (*models.App, error) {
	var app models.App
	err := s.db.Where("app_id = ? AND app_secret = ?", appID, appSecret).First(&app).Error
	if err != nil {
		return nil, err
	}
	return &app, nil
}

// IncrementCallCount 增加调用次数
func (s *AppService) IncrementCallCount(appID string) error {
	return s.db.Model(&models.App{}).Where("app_id = ?", appID).
		Update("total_calls", gorm.Expr("total_calls + ?", 1)).Error
}

// GetUserByID 根据用户ID获取用户信息
func (s *AppService) GetUserByID(userID uint) (*models.User, error) {
	var user models.User
	err := s.db.First(&user, userID).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// DeductUserBalance 扣除用户积分
func (s *AppService) DeductUserBalance(userID uint, amount int64, reason string, operatorID uint) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 扣除积分
		err := tx.Model(&models.User{}).Where("id = ?", userID).
			Update("balance", gorm.Expr("balance - ?", amount)).Error
		if err != nil {
			return err
		}

		// 记录积分变动
		balanceLog := &models.BalanceLog{
			UserID:       userID,
			ChangeAmount: -amount,
			Reason:       reason,
			OperatorID:   operatorID,
		}

		return tx.Create(balanceLog).Error
	})
}

// AddUserBalance 增加用户积分
func (s *AppService) AddUserBalance(userID uint, amount int64, reason string, operatorID uint) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 增加积分
		err := tx.Model(&models.User{}).Where("id = ?", userID).
			Update("balance", gorm.Expr("balance + ?", amount)).Error
		if err != nil {
			return err
		}

		// 记录积分变动
		balanceLog := &models.BalanceLog{
			UserID:       userID,
			ChangeAmount: amount,
			Reason:       reason,
			OperatorID:   operatorID,
		}

		return tx.Create(balanceLog).Error
	})
}

// generateAppID 生成16位AppID
func (s *AppService) generateAppID() (string, error) {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	const length = 16

	for {
		id, err := s.generateRandomString(charset, length)
		if err != nil {
			return "", err
		}

		// 检查是否已存在
		var count int64
		s.db.Model(&models.App{}).Where("app_id = ?", id).Count(&count)
		if count == 0 {
			return id, nil
		}
	}
}

// generateAppSecret 生成32位AppSecret
func (s *AppService) generateAppSecret() (string, error) {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	const length = 32

	return s.generateRandomString(charset, length)
}

// generateRandomString 生成随机字符串
func (s *AppService) generateRandomString(charset string, length int) (string, error) {
	b := make([]byte, length)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}

	var result strings.Builder
	for _, v := range b {
		result.WriteByte(charset[v%byte(len(charset))])
	}

	return result.String(), nil
}
