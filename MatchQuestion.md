# 本文介绍了如何通过多步筛选和 MySQL 查询实现高效的题库搜索，以提高精准度与响应速度。以下是整个流程的详细描述，涵盖了类型筛选、字符长度筛选、编辑距离筛选和选项匹配等步骤。


- 在经过前面的业务后我们ProcessContext内SaveQuestion数组中已经存储了如下的值
{
    type : "",
    concontent : "",
    cleancontent : "",
    options : {
        "" : "",
        "" : "",
        "" : "",
        "" : ""
    },
    user_url : "",
    hash_key : "",
}



### mysql搜题业务逻辑非hash_key索引方法（下述筛选只筛选题库里verified=1已确认的题目）

1. 根据题目类型进行筛选

    - 通过题目类型 (type 字段) 在数据库中进行初步筛选。
	- 通过 SaveQuestion 数组中的 type 字段与数据库中的 type 进行匹配，筛选出已确认（verified = 1）的题目。
        SELECT * 
        FROM questions
        WHERE verified = 1 
        AND question_type = ?;

2. 根据字符长度进行筛选

    - 通过 cleancontent 字段的字符长度，在题库中进一步缩小候选范围。
	- 操作：计算 SaveQuestion 中 cleancontent 字段的字符长度，然后查询数据库中的 question_len 字段，筛选出长度相差不超过 ±5 字符的题目。

        // 计算 cleancontent 的字符长度
        cleancontentLen := len([]rune(SaveQuestion.cleancontent))

        // SQL 查询，假设误差为 ±5
        SELECT * 
        FROM questions 
        WHERE verified = 1 
        AND ABS(question_len - ?) <= 5;

3. 选项集交集与数量判断

        - 目标：根据 SaveQuestion 中的 options 数组进行进一步筛选，确保选项的命中率高。
	    - 操作：在筛选出的题目中，计算 SaveQuestion.options 与每道题目选项之间的交集，判断交集的数量是否大于或等于 3。

        // 假设 options 存储为 map
        userOptions := SaveQuestion.options
        for _, dbQuestion := range dbQuestions {
            intersectionCount := 0
            for key, value := range dbQuestion.options {
                if userOptions[key] == value {
                    intersectionCount++
                }
            }

            // 如果命中 >= 3 个选项，则返回该题目
            if intersectionCount >= 3 {
                // 该题目命中，加入结果
            }
        }

4. 最终返回结果，若不存在命中结果则继续执行。下一步参阅main.md中的提交solve模型解答。

