#!/bin/bash

# API测试脚本
set -e

BASE_URL="http://localhost:8080"
API_BASE="$BASE_URL/api/v1"

echo "=== Solve Go API 测试脚本 ==="
echo "Base URL: $BASE_URL"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local description=$5
    
    echo -e "${YELLOW}测试: $description${NC}"
    echo "请求: $method $endpoint"
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$endpoint")
    fi
    
    # 分离响应体和状态码
    body=$(echo "$response" | head -n -1)
    status=$(echo "$response" | tail -n 1)
    
    if [ "$status" -eq "$expected_status" ]; then
        echo -e "${GREEN}✓ 成功 (状态码: $status)${NC}"
        echo "响应: $body"
    else
        echo -e "${RED}✗ 失败 (期望: $expected_status, 实际: $status)${NC}"
        echo "响应: $body"
    fi
    echo "----------------------------------------"
}

# 1. 健康检查
test_endpoint "GET" "$BASE_URL/health" "" 200 "健康检查"

# 2. 用户注册
register_data='{
    "phone": "13800138000",
    "password": "123456",
    "nickname": "测试用户",
    "sms_code": "123456"
}'
test_endpoint "POST" "$API_BASE/register" "$register_data" 200 "用户注册"

# 3. 管理员登录（使用默认管理员账号）
admin_login_data='{
    "phone": "13800000001",
    "password": "123456"
}'
echo -e "${YELLOW}获取管理员Token...${NC}"
admin_response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "$admin_login_data" \
    "$API_BASE/login")

admin_token=$(echo "$admin_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -n "$admin_token" ]; then
    echo -e "${GREEN}✓ 管理员登录成功${NC}"
    echo "Token: ${admin_token:0:20}..."
    
    # 4. 激活用户
    echo -e "${YELLOW}激活测试用户...${NC}"
    curl -s -X POST \
        -H "Authorization: Bearer $admin_token" \
        "$API_BASE/admin/users/2/activate" > /dev/null
    echo -e "${GREEN}✓ 用户激活完成${NC}"
else
    echo -e "${RED}✗ 管理员登录失败${NC}"
    exit 1
fi

# 5. 用户登录
user_login_data='{
    "phone": "13800138000",
    "password": "123456"
}'
echo -e "${YELLOW}用户登录...${NC}"
user_response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "$user_login_data" \
    "$API_BASE/login")

user_token=$(echo "$user_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -n "$user_token" ]; then
    echo -e "${GREEN}✓ 用户登录成功${NC}"
    echo "Token: ${user_token:0:20}..."
else
    echo -e "${RED}✗ 用户登录失败${NC}"
    echo "响应: $user_response"
    exit 1
fi

# 6. 获取用户信息
test_endpoint "GET" "$API_BASE/user/profile" "" 200 "获取用户信息" \
    -H "Authorization: Bearer $user_token"

# 7. 创建应用
app_data='{
    "name": "测试应用"
}'
echo -e "${YELLOW}创建应用...${NC}"
app_response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $user_token" \
    -d "$app_data" \
    "$API_BASE/apps")

app_id=$(echo "$app_response" | grep -o '"app_id":"[^"]*"' | cut -d'"' -f4)
app_secret=$(echo "$app_response" | grep -o '"app_secret":"[^"]*"' | cut -d'"' -f4)

if [ -n "$app_id" ] && [ -n "$app_secret" ]; then
    echo -e "${GREEN}✓ 应用创建成功${NC}"
    echo "App ID: $app_id"
    echo "App Secret: ${app_secret:0:10}..."
else
    echo -e "${RED}✗ 应用创建失败${NC}"
    echo "响应: $app_response"
    exit 1
fi

# 8. 获取应用列表
test_endpoint "GET" "$API_BASE/apps" "" 200 "获取应用列表" \
    -H "Authorization: Bearer $user_token"

# 9. 测试解题API（需要有效的图片URL和模型配置）
echo -e "${YELLOW}注意: 解题API测试需要配置有效的模型API密钥${NC}"
solve_data="{
    \"app_id\": \"$app_id\",
    \"app_secret\": \"$app_secret\",
    \"image_url\": \"https://example.com/test-question.jpg\"
}"

echo -e "${YELLOW}测试解题API (可能失败，因为需要真实的模型配置)${NC}"
curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "$solve_data" \
    "$API_BASE/solve/question" | head -c 200
echo
echo "----------------------------------------"

# 10. 管理员功能测试
echo -e "${YELLOW}测试管理员功能...${NC}"

# 获取用户统计
test_endpoint "GET" "$API_BASE/admin/users/stats" "" 200 "获取用户统计" \
    -H "Authorization: Bearer $admin_token"

# 获取用户列表
test_endpoint "GET" "$API_BASE/admin/users?page=1&page_size=10" "" 200 "获取用户列表" \
    -H "Authorization: Bearer $admin_token"

echo -e "${GREEN}=== 测试完成 ===${NC}"
echo "注意事项："
echo "1. 解题API需要配置有效的模型API密钥才能正常工作"
echo "2. 请在.env文件中配置QWEN_API_KEY和DEEPSEEK_API_KEY"
echo "3. 确保MySQL和Redis服务正常运行"
