package services

import (
	"fmt"
	"solve-go-api/internal/models"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UserService 用户服务
type UserService struct {
	db *gorm.DB
}

// NewUserService 创建用户服务
func NewUserService(db *gorm.DB) *UserService {
	return &UserService{db: db}
}

// CreateUser 创建用户
func (s *UserService) CreateUser(phone, password, nickname string) (*models.User, error) {
	// 检查手机号是否已存在
	var count int64
	s.db.Model(&models.User{}).Where("phone = ?", phone).Count(&count)
	if count > 0 {
		return nil, fmt.Errorf("手机号已存在")
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	user := &models.User{
		Phone:    phone,
		Password: string(hashedPassword),
		Role:     "user",
		Nickname: nickname,
		Balance:  0,
		IsActive: models.UserStatusPending, // 默认审核状态，需要管理员审核
	}

	err = s.db.Create(user).Error
	if err != nil {
		return nil, err
	}

	return user, nil
}

// GetUserByPhone 根据手机号获取用户
func (s *UserService) GetUserByPhone(phone string) (*models.User, error) {
	var user models.User
	err := s.db.Where("phone = ?", phone).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(id uint) (*models.User, error) {
	var user models.User
	err := s.db.First(&user, id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// ValidateUser 验证用户登录
func (s *UserService) ValidateUser(phone, password string) (*models.User, error) {
	user, err := s.GetUserByPhone(phone)
	if err != nil {
		return nil, fmt.Errorf("用户不存在")
	}

	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
	if err != nil {
		return nil, fmt.Errorf("密码错误")
	}

	// 检查用户状态
	switch user.IsActive {
	case models.UserStatusDisabled:
		return nil, fmt.Errorf("账户已被禁用")
	case models.UserStatusPending:
		return nil, fmt.Errorf("账户正在审核中，请等待管理员审核")
	case models.UserStatusActive:
		// 正常状态，继续登录流程
	default:
		return nil, fmt.Errorf("账户状态异常")
	}

	// 更新最后登录时间
	s.db.Model(user).Update("updated_at", gorm.Expr("NOW()"))

	return user, nil
}

// UpdateUser 更新用户信息
func (s *UserService) UpdateUser(id uint, updates map[string]interface{}) error {
	return s.db.Model(&models.User{}).Where("id = ?", id).Updates(updates).Error
}

// ChangePassword 修改密码
func (s *UserService) ChangePassword(id uint, newPassword string) error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	return s.db.Model(&models.User{}).Where("id = ?", id).Update("password", string(hashedPassword)).Error
}

// GetUsers 获取用户列表
func (s *UserService) GetUsers(page, pageSize int, filters map[string]interface{}) ([]models.User, int64, error) {
	var users []models.User
	var total int64

	query := s.db.Model(&models.User{})

	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "role":
			query = query.Where("role = ?", value)
		case "is_active":
			query = query.Where("is_active = ?", value)
		case "phone":
			query = query.Where("phone LIKE ?", "%"+value.(string)+"%")
		case "nickname":
			query = query.Where("nickname LIKE ?", "%"+value.(string)+"%")
		}
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&users).Error
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// ActivateUser 激活用户（审核通过）
func (s *UserService) ActivateUser(id uint) error {
	return s.db.Model(&models.User{}).Where("id = ?", id).Update("is_active", models.UserStatusActive).Error
}

// DeactivateUser 禁用用户
func (s *UserService) DeactivateUser(id uint) error {
	return s.db.Model(&models.User{}).Where("id = ?", id).Update("is_active", models.UserStatusDisabled).Error
}

// SetUserPending 设置用户为审核状态
func (s *UserService) SetUserPending(id uint) error {
	return s.db.Model(&models.User{}).Where("id = ?", id).Update("is_active", models.UserStatusPending).Error
}

// GetPendingUsers 获取待审核用户列表
func (s *UserService) GetPendingUsers(page, pageSize int) ([]models.User, int64, error) {
	var users []models.User
	var total int64

	query := s.db.Model(&models.User{}).Where("is_active = ?", models.UserStatusPending)

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&users).Error
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// GetBalanceLogs 获取用户积分变动记录
func (s *UserService) GetBalanceLogs(userID uint, page, pageSize int) ([]models.BalanceLog, int64, error) {
	var logs []models.BalanceLog
	var total int64

	query := s.db.Model(&models.BalanceLog{}).Where("user_id = ?", userID).Preload("Operator")

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetUserStats 获取用户统计信息
func (s *UserService) GetUserStats() (map[string]interface{}, error) {
	var total int64
	var active int64
	var disabled int64
	var pending int64

	// 总用户数
	err := s.db.Model(&models.User{}).Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 正常用户数
	err = s.db.Model(&models.User{}).Where("is_active = ?", models.UserStatusActive).Count(&active).Error
	if err != nil {
		return nil, err
	}

	// 禁用用户数
	err = s.db.Model(&models.User{}).Where("is_active = ?", models.UserStatusDisabled).Count(&disabled).Error
	if err != nil {
		return nil, err
	}

	// 审核中用户数
	err = s.db.Model(&models.User{}).Where("is_active = ?", models.UserStatusPending).Count(&pending).Error
	if err != nil {
		return nil, err
	}

	// 按角色统计
	var roleStats []map[string]interface{}
	err = s.db.Model(&models.User{}).
		Select("role, COUNT(*) as count").
		Group("role").
		Scan(&roleStats).Error
	if err != nil {
		return nil, err
	}

	// 按状态统计
	var statusStats []map[string]interface{}
	err = s.db.Model(&models.User{}).
		Select("is_active, COUNT(*) as count").
		Group("is_active").
		Scan(&statusStats).Error
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"total":     total,
		"active":    active,
		"disabled":  disabled,
		"pending":   pending,
		"by_role":   roleStats,
		"by_status": statusStats,
	}

	return stats, nil
}
