# 用户审核功能 API 指南

## 📋 功能概述

用户注册审核功能已经实现，新用户注册后默认为审核状态，需要管理员审核后才能正常使用系统。

### 用户状态说明

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 禁用 | 用户被禁用，无法登录和使用系统 |
| 1 | 正常 | 用户正常，可以登录和使用系统 |
| 2 | 审核中 | 新注册用户默认状态，需要管理员审核 |

## 🔄 业务流程

### 用户注册流程
1. 用户提交注册信息
2. 系统验证短信验证码
3. 创建用户，状态设置为 `2`（审核中）
4. 返回注册成功，提示等待审核

### 管理员审核流程
1. 管理员查看待审核用户列表
2. 审核用户信息
3. 选择审核通过或拒绝
4. 系统更新用户状态

## 🚀 API 接口

### 1. 用户注册

**接口地址**: `POST /api/v1/register`

**请求参数**:
```json
{
  "phone": "13800000000",
  "password": "123456",
  "nickname": "测试用户",
  "sms_code": "123456"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "注册成功，账户正在审核中，请等待管理员审核",
  "data": {
    "id": 123,
    "phone": "13800000000",
    "nickname": "测试用户",
    "is_active": 2,
    "status": "审核中"
  }
}
```

### 2. 用户登录

**接口地址**: `POST /api/v1/login`

**审核中用户登录响应**:
```json
{
  "code": 401,
  "message": "账户正在审核中，请等待管理员审核"
}
```

**禁用用户登录响应**:
```json
{
  "code": 401,
  "message": "账户已被禁用"
}
```

### 3. 获取待审核用户列表

**接口地址**: `GET /api/v1/admin/users/pending`

**权限要求**: 超级管理员

**请求参数**:
- `page`: 页码（可选，默认1）
- `page_size`: 每页数量（可选，默认20）

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "users": [
      {
        "id": 123,
        "phone": "13800000000",
        "nickname": "测试用户",
        "is_active": 2,
        "created_at": "2024-01-01T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 5,
      "total_page": 1
    }
  }
}
```

### 4. 审核通过用户

**接口地址**: `POST /api/v1/admin/users/{id}/approve`

**权限要求**: 超级管理员

**响应示例**:
```json
{
  "code": 200,
  "message": "用户审核通过"
}
```

### 5. 审核拒绝用户

**接口地址**: `POST /api/v1/admin/users/{id}/reject`

**权限要求**: 超级管理员

**响应示例**:
```json
{
  "code": 200,
  "message": "用户审核拒绝"
}
```

### 6. 获取用户统计信息

**接口地址**: `GET /api/v1/admin/users/stats`

**权限要求**: 超级管理员

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "active": 80,
    "disabled": 5,
    "pending": 15,
    "by_role": [
      {"role": "admin", "count": 1},
      {"role": "manager", "count": 2},
      {"role": "user", "count": 97}
    ],
    "by_status": [
      {"is_active": 0, "count": 5},
      {"is_active": 1, "count": 80},
      {"is_active": 2, "count": 15}
    ]
  }
}
```

## 🛠️ 数据库变更

### 字段变更
- `hook_user.is_active`: `BOOLEAN` → `TINYINT`
- 默认值: `TRUE` → `2`

### 迁移脚本
运行 `scripts/migrate_user_status.sql` 进行数据库迁移。

## 🧪 测试

### 测试脚本
运行 `scripts/test_user_status.go` 验证用户状态功能。

```bash
go run scripts/test_user_status.go
```

### 测试场景
1. 新用户注册 → 状态为审核中
2. 审核中用户登录 → 提示等待审核
3. 管理员审核通过 → 用户可正常登录
4. 管理员审核拒绝 → 用户被禁用

## 📝 注意事项

1. **管理员用户**: 创建时默认为正常状态（`is_active = 1`）
2. **普通用户**: 注册时默认为审核状态（`is_active = 2`）
3. **向下兼容**: 原有的激活/禁用接口仍然可用
4. **权限控制**: 只有超级管理员可以进行用户审核操作

## 🔧 前端适配建议

1. **注册页面**: 显示审核提示信息
2. **登录页面**: 处理审核状态的错误提示
3. **管理后台**: 添加待审核用户列表和审核操作
4. **用户列表**: 显示用户状态标识（正常/禁用/审核中）
