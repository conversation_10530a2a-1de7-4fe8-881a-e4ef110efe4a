# 用户注册审核功能部署指南

## 🎯 部署概述

用户注册审核功能已经完成开发和测试，现在可以部署到生产环境。

## ✅ 功能验证

### 1. 编译测试
```bash
go build -o solve-go-api .
```
✅ 编译成功，无错误

### 2. 数据库迁移测试
```bash
go run scripts/migrate_user_status.go
```
✅ 数据库字段类型已正确 (tinyint)

### 3. 功能演示测试
```bash
go run scripts/demo_user_audit.go
```
✅ 用户审核流程完整运行

## 🚀 部署步骤

### 步骤 1: 数据库迁移
如果数据库中的 `is_active` 字段还是 `BOOLEAN` 类型，运行迁移脚本：

```bash
go run scripts/migrate_user_status.go
```

该脚本会：
- 检查当前字段类型
- 如需要，将 `BOOLEAN` 转换为 `TINYINT`
- 迁移现有数据 (true→1, false→0)
- 创建备份表 `hook_user_backup`
- 确保管理员用户状态正确

### 步骤 2: 代码部署
直接部署当前代码，所有修改已完成：

```bash
# 编译
go build -o solve-go-api .

# 部署到服务器
# (根据你的部署流程)
```

### 步骤 3: 验证部署
运行测试脚本验证功能：

```bash
go run scripts/test_user_status.go
```

## 📋 新增 API 接口

### 管理员审核接口
- `GET /api/v1/admin/users/pending` - 获取待审核用户列表
- `POST /api/v1/admin/users/:id/approve` - 审核通过用户
- `POST /api/v1/admin/users/:id/reject` - 审核拒绝用户

### 修改的接口
- `POST /api/v1/register` - 返回信息包含审核状态
- `POST /api/v1/login` - 增加审核状态错误提示
- `GET /api/v1/admin/users/stats` - 统计信息包含新状态

## 🔄 业务流程变更

### 新用户注册流程
1. 用户提交注册信息
2. 系统验证短信验证码
3. 创建用户，状态设置为 `2`（审核中）
4. 返回注册成功，提示等待审核

### 管理员审核流程
1. 管理员访问 `/api/v1/admin/users/pending` 查看待审核用户
2. 选择用户进行审核
3. 调用 `/api/v1/admin/users/:id/approve` 通过审核
4. 或调用 `/api/v1/admin/users/:id/reject` 拒绝审核

### 用户登录流程
- 状态为 `1` 的用户可以正常登录
- 状态为 `2` 的用户登录时提示"账户正在审核中"
- 状态为 `0` 的用户登录时提示"账户已被禁用"

## 📊 用户状态说明

| 状态值 | 状态名称 | 说明 | 用户行为 |
|--------|----------|------|----------|
| 0 | 禁用 | 用户被禁用 | 无法登录 |
| 1 | 正常 | 用户正常 | 可以登录使用 |
| 2 | 审核中 | 新注册用户 | 无法登录，等待审核 |

## 🛡️ 安全注意事项

1. **管理员权限**: 只有超级管理员可以进行用户审核操作
2. **数据备份**: 迁移脚本会自动创建备份表
3. **状态一致性**: 确保管理员用户状态为正常 (1)
4. **日志记录**: 所有审核操作都有日志记录

## 🧪 测试建议

### 功能测试
1. 新用户注册 → 验证状态为审核中
2. 审核中用户登录 → 验证提示信息
3. 管理员审核通过 → 验证用户可登录
4. 管理员审核拒绝 → 验证用户被禁用

### API 测试
```bash
# 获取待审核用户
curl -H "Authorization: Bearer <admin_token>" \
     http://localhost:8080/api/v1/admin/users/pending

# 审核通过用户
curl -X POST -H "Authorization: Bearer <admin_token>" \
     http://localhost:8080/api/v1/admin/users/123/approve

# 审核拒绝用户
curl -X POST -H "Authorization: Bearer <admin_token>" \
     http://localhost:8080/api/v1/admin/users/123/reject
```

## 📝 前端适配建议

1. **注册页面**: 显示"注册成功，请等待管理员审核"
2. **登录页面**: 处理审核状态的错误提示
3. **管理后台**: 添加用户审核管理界面
4. **用户列表**: 显示用户状态标识

## 🔧 故障排除

### 常见问题
1. **编译错误**: 确保所有依赖已更新
2. **数据库连接**: 检查数据库配置
3. **权限问题**: 确保管理员用户状态正确

### 回滚方案
如果需要回滚，可以使用备份表：
```sql
-- 恢复数据（如果需要）
DROP TABLE hook_user;
RENAME TABLE hook_user_backup TO hook_user;
```

## ✅ 部署检查清单

- [ ] 代码编译成功
- [ ] 数据库迁移完成
- [ ] 管理员用户状态正确
- [ ] 新用户注册测试通过
- [ ] 审核功能测试通过
- [ ] API 接口测试通过
- [ ] 前端界面适配完成

## 📞 技术支持

如有问题，请检查：
1. 日志文件中的错误信息
2. 数据库连接状态
3. 用户状态数据一致性

部署完成后，用户注册审核功能将正式生效！
