-- 添加DeepSeek模型配置到hook_models表
-- 使用前请确保已经设置了正确的DeepSeek API Key

-- 检查是否已存在deepseek模型配置
SELECT 
    model_name,
    model_url,
    model_type,
    temperature,
    top_p,
    repetition_penalty,
    presence_penalty,
    response_format
FROM hook_models 
WHERE model_name LIKE '%deepseek%';

-- 如果不存在，则插入deepseek-chat模型配置
INSERT INTO hook_models (
    model_name,
    model_url,
    model_key,
    model_type,
    role_system,
    role_user,
    temperature,
    top_p,
    top_k,
    repetition_penalty,
    presence_penalty,
    response_format,
    created_at,
    updated_at
) VALUES (
    'deepseek-chat',
    'https://api.deepseek.com/chat/completions',
    '', -- 需要在环境变量中配置或手动设置
    'solve',
    '你是一个专业的题目解答助手，请根据题目内容给出正确答案和详细解析。',
    '请解答这道题目，返回JSON格式：{"answer":"答案","analysis":"解析内容"}',
    0.3,
    0.9,
    50,
    1.0, -- 这个值会映射到deepseek的frequency_penalty
    0.0,
    'json_object',
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    model_url = VALUES(model_url),
    role_system = VALUES(role_system),
    role_user = VALUES(role_user),
    temperature = VALUES(temperature),
    top_p = VALUES(top_p),
    top_k = VALUES(top_k),
    repetition_penalty = VALUES(repetition_penalty),
    presence_penalty = VALUES(presence_penalty),
    response_format = VALUES(response_format),
    updated_at = NOW();

-- 验证插入结果
SELECT 
    id,
    model_name,
    model_url,
    model_type,
    role_system,
    role_user,
    temperature,
    top_p,
    top_k,
    repetition_penalty,
    presence_penalty,
    response_format,
    created_at,
    updated_at
FROM hook_models 
WHERE model_name = 'deepseek-chat';

-- 显示所有solve类型的模型配置
SELECT 
    model_name,
    model_type,
    temperature,
    top_p,
    repetition_penalty,
    presence_penalty,
    response_format
FROM hook_models 
WHERE model_type = 'solve'
ORDER BY model_name;
