package main

import (
	"fmt"
	"log"
	"os"
	"solve-go-api/internal/config"
	"solve-go-api/internal/database"
	"solve-go-api/internal/services"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库连接
	db, err := database.InitMySQL(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 创建模型服务
	modelService := services.NewModelService(db)

	fmt.Println("=== DeepSeek模型配置验证 ===")

	// 1. 检查deepseek模型是否存在
	fmt.Println("\n1. 检查deepseek模型配置...")
	deepseekModel, err := modelService.GetModelByName("deepseek-chat")
	if err != nil {
		fmt.Printf("❌ 未找到deepseek-chat模型配置: %v\n", err)
		fmt.Println("请运行 scripts/add_deepseek_model.sql 来添加模型配置")
		os.Exit(1)
	}

	fmt.Printf("✅ 找到deepseek-chat模型配置\n")
	fmt.Printf("   模型名称: %s\n", deepseekModel.ModelName)
	fmt.Printf("   模型URL: %s\n", deepseekModel.ModelURL)
	fmt.Printf("   模型类型: %s\n", deepseekModel.ModelType)
	fmt.Printf("   温度参数: %.2f\n", deepseekModel.Temperature)
	fmt.Printf("   TopP参数: %.2f\n", deepseekModel.TopP)
	fmt.Printf("   重复惩罚: %.2f (将映射为frequency_penalty)\n", deepseekModel.RepetitionPenalty)
	fmt.Printf("   存在惩罚: %.2f\n", deepseekModel.PresencePenalty)
	fmt.Printf("   响应格式: %s\n", deepseekModel.ResponseFormat)

	// 2. 验证必要字段
	fmt.Println("\n2. 验证必要字段...")

	if deepseekModel.ModelURL == "" {
		fmt.Println("❌ 模型URL为空")
	} else {
		fmt.Printf("✅ 模型URL: %s\n", deepseekModel.ModelURL)
	}

	if deepseekModel.ModelKey == "" {
		fmt.Println("❌ 模型Key为空，请设置环境变量或在数据库中配置")
	} else {
		fmt.Printf("✅ 模型Key已配置 (长度: %d)\n", len(deepseekModel.ModelKey))
	}

	if deepseekModel.ModelType != "solve" {
		fmt.Printf("❌ 模型类型错误，期望: solve, 实际: %s\n", deepseekModel.ModelType)
	} else {
		fmt.Println("✅ 模型类型正确: solve")
	}

	if deepseekModel.RoleSystem == "" {
		fmt.Println("❌ System角色内容为空")
	} else {
		fmt.Printf("✅ System角色内容已配置 (长度: %d)\n", len(deepseekModel.RoleSystem))
	}

	if deepseekModel.RoleUser == "" {
		fmt.Println("❌ User角色内容为空")
	} else {
		fmt.Printf("✅ User角色内容已配置 (长度: %d)\n", len(deepseekModel.RoleUser))
	}

	// 3. 检查参数范围
	fmt.Println("\n3. 检查参数范围...")

	if deepseekModel.Temperature < 0 || deepseekModel.Temperature > 2 {
		fmt.Printf("❌ Temperature参数超出范围 [0-2]: %.2f\n", deepseekModel.Temperature)
	} else {
		fmt.Printf("✅ Temperature参数正常: %.2f\n", deepseekModel.Temperature)
	}

	if deepseekModel.TopP < 0 || deepseekModel.TopP > 1 {
		fmt.Printf("❌ TopP参数超出范围 [0-1]: %.2f\n", deepseekModel.TopP)
	} else {
		fmt.Printf("✅ TopP参数正常: %.2f\n", deepseekModel.TopP)
	}

	if deepseekModel.RepetitionPenalty < -2 || deepseekModel.RepetitionPenalty > 2 {
		fmt.Printf("❌ RepetitionPenalty参数超出范围 [-2,2]: %.2f\n", deepseekModel.RepetitionPenalty)
	} else {
		fmt.Printf("✅ RepetitionPenalty参数正常: %.2f (将映射为frequency_penalty)\n", deepseekModel.RepetitionPenalty)
	}

	if deepseekModel.PresencePenalty < -2 || deepseekModel.PresencePenalty > 2 {
		fmt.Printf("❌ PresencePenalty参数超出范围 [-2,2]: %.2f\n", deepseekModel.PresencePenalty)
	} else {
		fmt.Printf("✅ PresencePenalty参数正常: %.2f\n", deepseekModel.PresencePenalty)
	}

	// 4. 比较qwen-plus和deepseek配置
	fmt.Println("\n4. 比较qwen-plus和deepseek配置...")
	qwenModel, err := modelService.GetModelByName("qwen-plus")
	if err != nil {
		fmt.Printf("⚠️  未找到qwen-plus模型配置: %v\n", err)
	} else {
		fmt.Println("参数对比:")
		fmt.Printf("   Temperature: qwen=%.2f, deepseek=%.2f\n", qwenModel.Temperature, deepseekModel.Temperature)
		fmt.Printf("   TopP: qwen=%.2f, deepseek=%.2f\n", qwenModel.TopP, deepseekModel.TopP)
		fmt.Printf("   RepetitionPenalty: qwen=%.2f, deepseek=%.2f\n", qwenModel.RepetitionPenalty, deepseekModel.RepetitionPenalty)
		fmt.Printf("   PresencePenalty: qwen=%.2f, deepseek=%.2f\n", qwenModel.PresencePenalty, deepseekModel.PresencePenalty)
	}

	fmt.Println("\n=== 验证完成 ===")
	fmt.Println("如果所有检查都通过，deepseek模型配置正确！")
}
