version: '3.8'

services:
  # 注意：本地MySQL和Redis服务已注释，因为项目使用远程数据库
  # 如需本地开发，请取消注释并修改API服务的环境变量配置

  # MySQL数据库 (本地开发用，已注释)
  # mysql:
  #   image: mysql:8.0
  #   container_name: solve-mysql
  #   restart: unless-stopped
  #   environment:
  #     MYSQL_ROOT_PASSWORD: root123456
  #     MYSQL_DATABASE: solve_api
  #     MYSQL_USER: solve_user
  #     MYSQL_PASSWORD: solve_pass
  #   ports:
  #     - "3306:3306"
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #     - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init.sql
  #   command: --default-authentication-plugin=mysql_native_password
  #   networks:
  #     - solve-network

  # Redis缓存 (本地开发用，已注释)
  # redis:
  #   image: redis:7-alpine
  #   container_name: solve-redis
  #   restart: unless-stopped
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   command: redis-server --appendonly yes
  #   networks:
  #     - solve-network

  # API服务
  api:
    build: .
    container_name: solve-api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # 服务器配置
      SERVER_PORT: 8080
      GIN_MODE: debug
      
      # 数据库配置 (使用远程数据库)
      # 注意：这里配置的是远程数据库，不是本地Docker容器
      DB_HOST: ***********
      DB_PORT: 3380
      DB_USERNAME: gmdns
      DB_PASSWORD: Suyan15913..
      DB_DATABASE: solve_web
      DB_CHARSET: utf8mb4

      # Redis配置 (使用远程Redis)
      # 注意：这里配置的是远程Redis，不是本地Docker容器
      REDIS_HOST: ***********
      REDIS_PORT: 6379
      REDIS_PASSWORD: Suyan15913..
      REDIS_DB: 0
      
      # JWT配置
      JWT_SECRET: your_jwt_secret_key_here
      
      # 模型配置
      MODEL_OCR: qwen-vl-plus
      MODEL_SOLVE: qwen-plus
      
      # 模型API密钥（请替换为实际的密钥）
      QWEN_API_KEY: your_qwen_api_key
      DEEPSEEK_API_KEY: your_deepseek_api_key
    # depends_on:
    #   - mysql  # 注释掉，因为使用远程数据库
    #   - redis  # 注释掉，因为使用远程Redis
    networks:
      - solve-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: solve-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - api
    networks:
      - solve-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  solve-network:
    driver: bridge
