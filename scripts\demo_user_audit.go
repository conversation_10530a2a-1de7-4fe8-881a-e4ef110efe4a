package main

import (
	"fmt"
	"log"
	"os"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// User 用户模型
type User struct {
	ID       uint   `gorm:"primaryKey;autoIncrement"`
	Phone    string `gorm:"type:varchar(20);uniqueIndex;not null"`
	Password string `gorm:"type:varchar(255);not null"`
	Role     string `gorm:"type:enum('admin','manager','user');default:'user'"`
	Nickname string `gorm:"type:varchar(100)"`
	Balance  int64  `gorm:"default:0"`
	IsActive int    `gorm:"type:tinyint;default:2"`
}

// TableName 指定表名
func (User) TableName() string {
	return "hook_user"
}

// 用户状态常量
const (
	UserStatusDisabled = 0 // 禁用
	UserStatusActive   = 1 // 正常
	UserStatusPending  = 2 // 审核中
)

func main() {
	// 获取数据库连接信息
	host := getEnv("DB_HOST", "***********")
	port := getEnv("DB_PORT", "3380")
	user := getEnv("DB_USER", "gmdns")
	password := getEnv("DB_PASSWORD", "Suyan15913..")
	dbname := getEnv("DB_NAME", "solve_web")

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		user, password, host, port, dbname)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	fmt.Println("🎭 用户审核功能演示")
	fmt.Println("==================")

	// 1. 创建一个测试用户（模拟注册）
	fmt.Println("\n📝 步骤1: 模拟用户注册...")

	testPhone := "13900000001"
	testNickname := "待审核测试用户"

	// 检查用户是否已存在
	var existingUser User
	result := db.Where("phone = ?", testPhone).First(&existingUser)
	if result.Error == nil {
		fmt.Printf("用户 %s 已存在，删除后重新创建...\n", testPhone)
		db.Delete(&existingUser)
	}

	// 创建新用户（默认审核状态）
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("123456"), bcrypt.DefaultCost)
	newUser := User{
		Phone:    testPhone,
		Password: string(hashedPassword),
		Role:     "user",
		Nickname: testNickname,
		Balance:  0,
		IsActive: UserStatusPending, // 默认审核状态
	}

	err = db.Create(&newUser).Error
	if err != nil {
		log.Fatal("创建用户失败:", err)
	}

	fmt.Printf("✅ 用户注册成功: %s (%s)\n", newUser.Nickname, newUser.Phone)
	fmt.Printf("   用户ID: %d\n", newUser.ID)
	fmt.Printf("   状态: %d (%s)\n", newUser.IsActive, getStatusName(newUser.IsActive))

	// 2. 查看待审核用户列表
	fmt.Println("\n📋 步骤2: 查看待审核用户列表...")

	var pendingUsers []User
	err = db.Where("is_active = ?", UserStatusPending).Find(&pendingUsers).Error
	if err != nil {
		log.Fatal("查询待审核用户失败:", err)
	}

	fmt.Printf("待审核用户数量: %d\n", len(pendingUsers))
	for _, user := range pendingUsers {
		fmt.Printf("  - ID: %d, 昵称: %s, 手机: %s, 状态: %s\n",
			user.ID, user.Nickname, user.Phone, getStatusName(user.IsActive))
	}

	// 3. 模拟管理员审核通过
	fmt.Println("\n✅ 步骤3: 模拟管理员审核通过...")

	err = db.Model(&User{}).Where("id = ?", newUser.ID).Update("is_active", UserStatusActive).Error
	if err != nil {
		log.Fatal("审核用户失败:", err)
	}

	// 查询更新后的用户状态
	var updatedUser User
	db.First(&updatedUser, newUser.ID)
	fmt.Printf("✅ 用户审核通过: %s\n", updatedUser.Nickname)
	fmt.Printf("   新状态: %d (%s)\n", updatedUser.IsActive, getStatusName(updatedUser.IsActive))

	// 4. 显示最终统计
	fmt.Println("\n📊 步骤4: 最终用户状态统计...")

	var stats []struct {
		IsActive int   `json:"is_active"`
		Count    int64 `json:"count"`
	}

	err = db.Model(&User{}).
		Select("is_active, COUNT(*) as count").
		Group("is_active").
		Order("is_active").
		Scan(&stats).Error
	if err != nil {
		log.Fatal("查询统计信息失败:", err)
	}

	fmt.Println("用户状态分布:")
	for _, stat := range stats {
		statusName := getStatusName(stat.IsActive)
		fmt.Printf("  状态 %d (%s): %d 个用户\n", stat.IsActive, statusName, stat.Count)
	}

	// 5. 清理测试数据
	fmt.Println("\n🧹 步骤5: 清理测试数据...")

	err = db.Delete(&updatedUser).Error
	if err != nil {
		log.Printf("⚠️  清理测试用户失败: %v", err)
	} else {
		fmt.Printf("✅ 测试用户已清理: %s\n", testNickname)
	}

	fmt.Println("\n🎉 用户审核功能演示完成!")
	fmt.Println("\n📋 功能总结:")
	fmt.Println("  1. 新用户注册时默认状态为 2 (审核中)")
	fmt.Println("  2. 管理员可以查看待审核用户列表")
	fmt.Println("  3. 管理员可以审核通过用户 (状态改为 1)")
	fmt.Println("  4. 管理员可以审核拒绝用户 (状态改为 0)")
	fmt.Println("  5. 只有状态为 1 的用户可以正常登录")
}

// getStatusName 获取状态名称
func getStatusName(status int) string {
	switch status {
	case UserStatusDisabled:
		return "禁用"
	case UserStatusActive:
		return "正常"
	case UserStatusPending:
		return "审核中"
	default:
		return "未知"
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
