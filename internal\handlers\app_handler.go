package handlers

import (
	"net/http"
	"solve-go-api/internal/services"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AppHandler 应用处理器
type AppHandler struct {
	appService  *services.AppService
	userService *services.UserService
}

// NewAppHandler 创建应用处理器
func NewAppHandler(appService *services.AppService, userService *services.UserService) *AppHandler {
	return &AppHandler{
		appService:  appService,
		userService: userService,
	}
}

// CreateAppRequest 创建应用请求
type CreateAppRequest struct {
	Name string `json:"name" binding:"required"`
}

// GetApps 获取用户应用列表
func (h *AppHandler) GetApps(c *gin.Context) {
	userID, _ := c.Get("user_id")

	apps, err := h.appService.GetAppsByUserID(userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取应用列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    apps,
	})
}

// CreateApp 创建应用
func (h *AppHandler) CreateApp(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req CreateAppRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	app, err := h.appService.CreateApp(userID.(uint), req.Name)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建应用失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "应用创建成功",
		"data":    app,
	})
}

// UpdateApp 更新应用
func (h *AppHandler) UpdateApp(c *gin.Context) {
	userID, _ := c.Get("user_id")
	appID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的应用ID",
		})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证应用所有权
	apps, err := h.appService.GetAppsByUserID(userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "验证应用所有权失败",
		})
		return
	}

	found := false
	for _, app := range apps {
		if app.ID == uint(appID) {
			found = true
			break
		}
	}

	if !found {
		c.JSON(http.StatusForbidden, gin.H{
			"code":    403,
			"message": "无权限操作此应用",
		})
		return
	}

	// 只允许更新名称
	allowedFields := map[string]interface{}{}
	if name, exists := updates["name"]; exists {
		allowedFields["name"] = name
	}

	if len(allowedFields) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "没有可更新的字段",
		})
		return
	}

	err = h.appService.UpdateApp(uint(appID), allowedFields)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新应用失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "应用更新成功",
	})
}

// DeleteApp 删除应用
func (h *AppHandler) DeleteApp(c *gin.Context) {
	userID, _ := c.Get("user_id")
	appID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的应用ID",
		})
		return
	}

	// 验证应用所有权
	apps, err := h.appService.GetAppsByUserID(userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "验证应用所有权失败",
		})
		return
	}

	found := false
	for _, app := range apps {
		if app.ID == uint(appID) {
			found = true
			break
		}
	}

	if !found {
		c.JSON(http.StatusForbidden, gin.H{
			"code":    403,
			"message": "无权限操作此应用",
		})
		return
	}

	err = h.appService.DeleteApp(uint(appID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除应用失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "应用删除成功",
	})
}

// ResetSecret 重置应用密钥
func (h *AppHandler) ResetSecret(c *gin.Context) {
	userID, _ := c.Get("user_id")
	appID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的应用ID",
		})
		return
	}

	// 验证应用所有权
	apps, err := h.appService.GetAppsByUserID(userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "验证应用所有权失败",
		})
		return
	}

	found := false
	for _, app := range apps {
		if app.ID == uint(appID) {
			found = true
			break
		}
	}

	if !found {
		c.JSON(http.StatusForbidden, gin.H{
			"code":    403,
			"message": "无权限操作此应用",
		})
		return
	}

	newSecret, err := h.appService.ResetAppSecret(uint(appID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "重置密钥失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "密钥重置成功",
		"data": gin.H{
			"app_secret": newSecret,
		},
	})
}

// GetAppLogs 获取应用调用日志
func (h *AppHandler) GetAppLogs(c *gin.Context) {
	userID, _ := c.Get("user_id")
	appID := c.Param("id")

	// 验证应用所有权
	apps, err := h.appService.GetAppsByUserID(userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "验证应用所有权失败",
		})
		return
	}

	found := false
	for _, app := range apps {
		if app.AppID == appID {
			found = true
			break
		}
	}

	if !found {
		c.JSON(http.StatusForbidden, gin.H{
			"code":    403,
			"message": "无权限查看此应用日志",
		})
		return
	}

	// TODO: 实现获取应用日志的逻辑
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "功能开发中",
	})
}
