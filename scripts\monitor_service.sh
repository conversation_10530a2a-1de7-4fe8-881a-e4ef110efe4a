#!/bin/bash

# 服务监控脚本 - 监控Go服务状态并自动重启

set -e

# 配置
SERVICE_NAME="solve-go-api"
BINARY_NAME="solve-api"
PORT=${PORT:-8080}
CHECK_INTERVAL=${CHECK_INTERVAL:-30}  # 检查间隔（秒）
MAX_FAILURES=${MAX_FAILURES:-3}       # 最大失败次数
RESTART_SCRIPT="./scripts/quick_restart.sh"
LOG_FILE="logs/monitor.log"

# 状态文件
STATUS_FILE="/tmp/${SERVICE_NAME}_monitor.status"
FAILURE_COUNT_FILE="/tmp/${SERVICE_NAME}_failures.count"

# 颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 创建日志目录
mkdir -p logs

# 日志函数
log_with_timestamp() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

info() { 
    echo -e "${GREEN}[INFO]${NC} $1"
    log_with_timestamp "INFO" "$1"
}

warn() { 
    echo -e "${YELLOW}[WARN]${NC} $1"
    log_with_timestamp "WARN" "$1"
}

error() { 
    echo -e "${RED}[ERROR]${NC} $1"
    log_with_timestamp "ERROR" "$1"
}

debug() { 
    echo -e "${BLUE}[DEBUG]${NC} $1"
    log_with_timestamp "DEBUG" "$1"
}

# 获取进程ID
get_pid() {
    lsof -ti:$PORT 2>/dev/null || pgrep -f "$BINARY_NAME" 2>/dev/null || echo ""
}

# 检查进程是否运行
is_process_running() {
    local pid=$(get_pid)
    if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 检查端口是否可访问
is_port_accessible() {
    if command -v nc &> /dev/null; then
        nc -z localhost $PORT 2>/dev/null
    elif command -v telnet &> /dev/null; then
        timeout 3 telnet localhost $PORT </dev/null >/dev/null 2>&1
    else
        # 使用curl作为备选
        curl -s --connect-timeout 3 "http://localhost:$PORT/health" > /dev/null 2>&1
    fi
}

# 健康检查
health_check() {
    if command -v curl &> /dev/null; then
        local response=$(curl -s --connect-timeout 5 --max-time 10 "http://localhost:$PORT/health" 2>/dev/null)
        if [ $? -eq 0 ] && echo "$response" | grep -q "ok\|success\|healthy"; then
            return 0
        fi
    fi
    return 1
}

# 获取失败次数
get_failure_count() {
    if [ -f "$FAILURE_COUNT_FILE" ]; then
        cat "$FAILURE_COUNT_FILE"
    else
        echo "0"
    fi
}

# 设置失败次数
set_failure_count() {
    echo "$1" > "$FAILURE_COUNT_FILE"
}

# 重置失败次数
reset_failure_count() {
    set_failure_count "0"
}

# 增加失败次数
increment_failure_count() {
    local count=$(get_failure_count)
    count=$((count + 1))
    set_failure_count "$count"
    echo "$count"
}

# 更新状态文件
update_status() {
    local status=$1
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "$timestamp|$status" > "$STATUS_FILE"
}

# 获取服务状态信息
get_service_info() {
    local pid=$(get_pid)
    if [ -n "$pid" ]; then
        echo "PID: $pid"
        if command -v ps &> /dev/null; then
            ps -p $pid -o pid,ppid,cmd,etime,pcpu,pmem --no-headers 2>/dev/null || true
        fi
    else
        echo "进程未运行"
    fi
}

# 尝试重启服务
restart_service() {
    warn "尝试重启服务..."
    
    if [ -f "$RESTART_SCRIPT" ] && [ -x "$RESTART_SCRIPT" ]; then
        info "使用重启脚本: $RESTART_SCRIPT"
        if $RESTART_SCRIPT; then
            info "服务重启成功"
            reset_failure_count
            update_status "RESTARTED"
            return 0
        else
            error "重启脚本执行失败"
            return 1
        fi
    else
        error "重启脚本不存在或不可执行: $RESTART_SCRIPT"
        return 1
    fi
}

# 发送通知（可扩展）
send_notification() {
    local message=$1
    local level=$2
    
    # 这里可以添加邮件、钉钉、微信等通知方式
    # 示例：发送到系统日志
    logger -t "$SERVICE_NAME-monitor" "$level: $message"
    
    # 示例：写入通知文件
    echo "$(date '+%Y-%m-%d %H:%M:%S') [$level] $message" >> "logs/notifications.log"
}

# 执行检查
perform_check() {
    local check_time=$(date '+%Y-%m-%d %H:%M:%S')
    debug "开始检查服务状态..."
    
    # 检查进程
    if ! is_process_running; then
        error "进程未运行"
        return 1
    fi
    
    # 检查端口
    if ! is_port_accessible; then
        error "端口 $PORT 不可访问"
        return 1
    fi
    
    # 健康检查
    if ! health_check; then
        warn "健康检查失败"
        return 1
    fi
    
    debug "服务状态正常"
    return 0
}

# 监控循环
monitor_loop() {
    info "开始监控服务 $SERVICE_NAME (端口: $PORT)"
    info "检查间隔: ${CHECK_INTERVAL}秒, 最大失败次数: $MAX_FAILURES"
    
    while true; do
        if perform_check; then
            # 检查成功
            reset_failure_count
            update_status "HEALTHY"
            
            # 每10次成功检查输出一次状态
            local success_count=$(( $(date +%s) / CHECK_INTERVAL % 10 ))
            if [ $success_count -eq 0 ]; then
                info "服务运行正常 $(get_service_info | head -1)"
            fi
        else
            # 检查失败
            local failure_count=$(increment_failure_count)
            warn "服务检查失败 (失败次数: $failure_count/$MAX_FAILURES)"
            update_status "UNHEALTHY"
            
            if [ $failure_count -ge $MAX_FAILURES ]; then
                error "服务连续失败 $failure_count 次，尝试重启"
                send_notification "服务 $SERVICE_NAME 连续失败 $failure_count 次，正在重启" "ERROR"
                
                if restart_service; then
                    info "服务重启成功"
                    send_notification "服务 $SERVICE_NAME 重启成功" "INFO"
                else
                    error "服务重启失败"
                    send_notification "服务 $SERVICE_NAME 重启失败，需要人工干预" "CRITICAL"
                    # 可以选择退出或继续监控
                    # exit 1
                fi
            fi
        fi
        
        sleep $CHECK_INTERVAL
    done
}

# 显示状态
show_status() {
    echo "=== 服务监控状态 ==="
    echo "服务名称: $SERVICE_NAME"
    echo "监控端口: $PORT"
    echo "检查间隔: ${CHECK_INTERVAL}秒"
    echo "最大失败次数: $MAX_FAILURES"
    echo "失败次数: $(get_failure_count)"
    
    if [ -f "$STATUS_FILE" ]; then
        local status_info=$(cat "$STATUS_FILE")
        echo "最后状态: $status_info"
    fi
    
    echo ""
    echo "=== 服务信息 ==="
    get_service_info
    
    echo ""
    echo "=== 最近日志 ==="
    if [ -f "$LOG_FILE" ]; then
        tail -n 10 "$LOG_FILE"
    else
        echo "暂无日志"
    fi
}

# 停止监控
stop_monitor() {
    local monitor_pid=$(pgrep -f "monitor_service.sh" | grep -v $$ | head -1)
    if [ -n "$monitor_pid" ]; then
        info "停止监控进程: $monitor_pid"
        kill $monitor_pid
        rm -f "$STATUS_FILE" "$FAILURE_COUNT_FILE"
        info "监控已停止"
    else
        warn "未找到运行中的监控进程"
    fi
}

# 显示帮助
show_help() {
    cat << EOF
服务监控脚本

用法: $0 [命令]

命令:
  start     开始监控 (默认)
  stop      停止监控
  status    显示监控状态
  check     执行一次检查
  help      显示帮助

环境变量:
  PORT              服务端口 (默认: 8080)
  CHECK_INTERVAL    检查间隔秒数 (默认: 30)
  MAX_FAILURES      最大失败次数 (默认: 3)

示例:
  $0                    # 开始监控
  $0 start              # 开始监控
  $0 status             # 查看状态
  PORT=9090 $0 start    # 监控9090端口
EOF
}

# 主函数
main() {
    local command=${1:-start}
    
    case "$command" in
        start)
            monitor_loop
            ;;
        stop)
            stop_monitor
            ;;
        status)
            show_status
            ;;
        check)
            if perform_check; then
                info "服务状态正常"
                exit 0
            else
                error "服务状态异常"
                exit 1
            fi
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 信号处理
trap 'info "收到退出信号，停止监控..."; exit 0' SIGTERM SIGINT

# 执行主函数
main "$@"
