-- 用户状态字段迁移脚本
-- 将 is_active 从 BOOLEAN 类型改为 TINYINT 类型
-- 0=禁用, 1=正常, 2=审核中

USE solve_web;

-- 1. 备份当前数据
CREATE TABLE IF NOT EXISTS hook_user_backup AS SELECT * FROM hook_user;

-- 2. 添加新的状态字段
ALTER TABLE hook_user ADD COLUMN is_active_new TINYINT DEFAULT 2 COMMENT '用户状态:0=禁用,1=正常,2=审核中';

-- 3. 迁移现有数据
-- 将原来的 true 转换为 1 (正常)
-- 将原来的 false 转换为 0 (禁用)
UPDATE hook_user SET is_active_new = CASE 
    WHEN is_active = 1 THEN 1  -- true -> 正常
    WHEN is_active = 0 THEN 0  -- false -> 禁用
    ELSE 2                     -- 默认 -> 审核中
END;

-- 4. 删除旧字段
ALTER TABLE hook_user DROP COLUMN is_active;

-- 5. 重命名新字段
ALTER TABLE hook_user CHANGE COLUMN is_active_new is_active TINYINT DEFAULT 2 COMMENT '用户状态:0=禁用,1=正常,2=审核中';

-- 6. 添加索引
ALTER TABLE hook_user ADD INDEX idx_is_active (is_active);

-- 7. 验证迁移结果
SELECT 
    is_active,
    CASE 
        WHEN is_active = 0 THEN '禁用'
        WHEN is_active = 1 THEN '正常'
        WHEN is_active = 2 THEN '审核中'
        ELSE '未知'
    END as status_name,
    COUNT(*) as count
FROM hook_user 
GROUP BY is_active;

-- 8. 显示迁移完成信息
SELECT 'User status migration completed successfully!' as message;
