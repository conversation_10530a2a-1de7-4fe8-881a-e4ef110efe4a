-- 重置特定管理员密码SQL脚本
-- 目标账号: 13800000001
-- 密码: 123456
-- bcrypt哈希值: $2a$10$9O2otaLC2wXu2m6XEuwxd.EUtHeyahknUl8IK7FCXdGNL95bahgrm

USE solve_web;

-- 显示当前目标用户信息
SELECT '=== 当前目标用户信息 ===' as info;
SELECT id, phone, role, nickname, is_active, created_at, updated_at
FROM hook_user 
WHERE phone = '13800000001';

-- 检查用户是否存在
SET @user_count = (SELECT COUNT(*) FROM hook_user WHERE phone = '13800000001');
SELECT CONCAT('用户存在检查: ', IF(@user_count > 0, '存在', '不存在')) as check_result;

-- 如果用户不存在，创建新用户
INSERT IGNORE INTO hook_user (phone, password, role, nickname, balance, is_active, created_at, updated_at)
VALUES ('13800000001', '$2a$10$9O2otaLC2wXu2m6XEuwxd.EUtHeyahknUl8IK7FCXdGNL95bahgrm', 'admin', '超级管理员', 0, 1, NOW(), NOW());

-- 重置目标用户密码为 123456，并确保账户激活和角色正确
UPDATE hook_user 
SET 
    password = '$2a$10$9O2otaLC2wXu2m6XEuwxd.EUtHeyahknUl8IK7FCXdGNL95bahgrm',
    role = 'admin',
    is_active = 1,
    updated_at = NOW()
WHERE phone = '13800000001';

-- 显示更新结果
SELECT '=== 密码重置完成 ===' as info;
SELECT CONCAT('影响行数: ', ROW_COUNT()) as result;

-- 显示更新后的用户信息
SELECT '=== 更新后的用户信息 ===' as info;
SELECT 
    id,
    phone as '手机号',
    role as '角色',
    nickname as '昵称',
    CASE WHEN is_active = 1 THEN '已激活' ELSE '未激活' END as '状态',
    created_at as '创建时间',
    updated_at as '更新时间'
FROM hook_user 
WHERE phone = '13800000001';

-- 显示登录信息
SELECT '=== 登录信息 ===' as info;
SELECT 
    '超级管理员' as '用户类型',
    '13800000001' as '手机号',
    '123456' as '密码',
    'admin' as '角色',
    '请使用此信息登录系统' as '说明';

-- 显示API测试命令
SELECT '=== API测试命令 ===' as info;
SELECT 'curl -X POST http://localhost:8080/api/v1/login -H "Content-Type: application/json" -d \'{"phone": "13800000001", "password": "123456"}\'' as '测试命令';

-- 验证所有管理员用户
SELECT '=== 所有管理员用户 ===' as info;
SELECT 
    id,
    phone as '手机号',
    role as '角色',
    nickname as '昵称',
    CASE WHEN is_active = 1 THEN '已激活' ELSE '未激活' END as '状态'
FROM hook_user 
WHERE role IN ('admin', 'manager')
ORDER BY role, phone;
