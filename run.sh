#!/bin/bash

# Solve Go API 简单启动脚本
# 用法: ./run.sh [start|stop|restart|status]

set -e

# 配置
SERVICE_NAME="solve-api"
SERVICE_PORT="8080"
LOG_FILE="logs/service.log"
PID_FILE="logs/service.pid"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建必要目录
create_dirs() {
    mkdir -p logs
}

# 检查环境
check_env() {
    # 检查Go是否安装
    if ! command -v go &> /dev/null; then
        log_error "Go未安装，请先安装Go"
        exit 1
    fi
    
    # 检查.env文件
    if [ ! -f .env ]; then
        log_warning ".env文件不存在"
        if [ -f .env.example ]; then
            log_info "从.env.example创建.env文件..."
            cp .env.example .env
            log_warning "请编辑.env文件配置正确的环境变量"
        else
            log_error "请创建.env文件并配置环境变量"
            exit 1
        fi
    fi
}

# 获取进程ID
get_pid() {
    if [ -f "$PID_FILE" ]; then
        cat "$PID_FILE"
    else
        pgrep -f "$SERVICE_NAME" | head -1
    fi
}

# 检查服务是否运行
is_running() {
    local pid=$(get_pid)
    if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 检查端口是否被占用
check_port() {
    if command -v lsof &> /dev/null; then
        if lsof -Pi :$SERVICE_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
            return 0
        fi
    elif command -v netstat &> /dev/null; then
        if netstat -ln 2>/dev/null | grep ":$SERVICE_PORT " >/dev/null; then
            return 0
        fi
    fi
    return 1
}

# 构建应用
build_app() {
    log_info "构建应用..."
    
    # 下载依赖
    go mod download
    
    # 构建
    go build -o "$SERVICE_NAME" main.go
    
    if [ ! -f "$SERVICE_NAME" ]; then
        log_error "构建失败"
        exit 1
    fi
    
    log_success "构建完成"
}

# 启动服务
start_service() {
    log_info "启动 $SERVICE_NAME 服务..."
    
    # 检查是否已经运行
    if is_running; then
        log_warning "服务已经在运行中"
        show_status
        return 0
    fi
    
    # 检查端口
    if check_port; then
        log_error "端口 $SERVICE_PORT 已被占用"
        exit 1
    fi
    
    # 检查环境
    check_env
    
    # 构建应用
    if [ ! -f "$SERVICE_NAME" ]; then
        build_app
    fi
    
    # 启动服务
    log_info "在后台启动服务..."
    nohup ./"$SERVICE_NAME" > "$LOG_FILE" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > "$PID_FILE"
    
    # 等待服务启动
    sleep 3
    
    # 检查是否启动成功
    if is_running; then
        log_success "服务启动成功 (PID: $pid)"
        log_info "服务运行在端口: $SERVICE_PORT"
        log_info "日志文件: $LOG_FILE"
        
        # 检查健康状态
        if command -v curl &> /dev/null; then
            sleep 2
            if curl -s http://localhost:$SERVICE_PORT/health >/dev/null 2>&1; then
                log_success "健康检查通过"
            else
                log_warning "健康检查失败，请查看日志"
            fi
        fi
    else
        log_error "服务启动失败，请查看日志: $LOG_FILE"
        exit 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止 $SERVICE_NAME 服务..."
    
    local pid=$(get_pid)
    
    if [ -z "$pid" ]; then
        log_warning "服务未运行"
        return 0
    fi
    
    # 尝试优雅停止
    log_info "发送停止信号 (PID: $pid)..."
    kill "$pid" 2>/dev/null || true
    
    # 等待进程停止
    local count=0
    while [ $count -lt 10 ]; do
        if ! kill -0 "$pid" 2>/dev/null; then
            break
        fi
        sleep 1
        count=$((count + 1))
    done
    
    # 检查是否已停止
    if kill -0 "$pid" 2>/dev/null; then
        log_warning "强制停止服务..."
        kill -9 "$pid" 2>/dev/null || true
        sleep 1
    fi
    
    # 清理PID文件
    rm -f "$PID_FILE"
    
    if is_running; then
        log_error "服务停止失败"
        exit 1
    else
        log_success "服务已停止"
    fi
}

# 重启服务
restart_service() {
    log_info "重启 $SERVICE_NAME 服务..."
    stop_service
    sleep 2
    start_service
}

# 显示状态
show_status() {
    log_info "检查 $SERVICE_NAME 服务状态..."
    
    if is_running; then
        local pid=$(get_pid)
        log_success "服务正在运行 (PID: $pid)"
        
        # 检查端口
        if check_port; then
            log_success "端口 $SERVICE_PORT 正在监听"
        else
            log_warning "端口 $SERVICE_PORT 未监听"
        fi
        
        # 检查健康状态
        if command -v curl &> /dev/null; then
            if curl -s http://localhost:$SERVICE_PORT/health >/dev/null 2>&1; then
                log_success "健康检查通过"
            else
                log_warning "健康检查失败"
            fi
        fi
        
        # 显示进程信息
        if command -v ps &> /dev/null; then
            echo ""
            echo "进程信息:"
            ps aux | grep "$SERVICE_NAME" | grep -v grep || true
        fi
    else
        log_warning "服务未运行"
    fi
}

# 查看日志
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        log_info "显示最近的日志 (按 Ctrl+C 退出):"
        tail -f "$LOG_FILE"
    else
        log_warning "日志文件不存在: $LOG_FILE"
    fi
}

# 显示帮助
show_help() {
    echo "Solve Go API 服务管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start    启动服务"
    echo "  stop     停止服务"
    echo "  restart  重启服务"
    echo "  status   查看状态"
    echo "  logs     查看日志"
    echo "  build    构建应用"
    echo "  help     显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 start     # 启动服务"
    echo "  $0 restart   # 重启服务"
    echo "  $0 status    # 查看状态"
    echo ""
}

# 主函数
main() {
    # 创建必要目录
    create_dirs
    
    # 解析命令
    case "${1:-start}" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        build)
            build_app
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
