package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// User 用户模型
type User struct {
	ID       uint   `gorm:"primaryKey;autoIncrement"`
	Phone    string `gorm:"type:varchar(20);uniqueIndex;not null"`
	Password string `gorm:"type:varchar(255);not null"`
	Role     string `gorm:"type:enum('admin','manager','user');default:'user'"`
	Nickname string `gorm:"type:varchar(100)"`
	Balance  int64  `gorm:"default:0"`
	IsActive int    `gorm:"type:tinyint;default:2"`
}

// TableName 指定表名
func (User) TableName() string {
	return "hook_user"
}

func main() {
	// 获取数据库连接信息
	host := getEnv("DB_HOST", "***********")
	port := getEnv("DB_PORT", "3380")
	user := getEnv("DB_USER", "gmdns")
	password := getEnv("DB_PASSWORD", "Suyan15913..")
	dbname := getEnv("DB_NAME", "solve_web")

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		user, password, host, port, dbname)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	fmt.Println("🔍 测试用户状态字段...")

	// 1. 检查用户状态分布
	fmt.Println("\n📊 用户状态分布:")
	var statusStats []struct {
		IsActive int   `json:"is_active"`
		Count    int64 `json:"count"`
	}

	err = db.Model(&User{}).
		Select("is_active, COUNT(*) as count").
		Group("is_active").
		Scan(&statusStats).Error
	if err != nil {
		log.Fatal("查询用户状态分布失败:", err)
	}

	for _, stat := range statusStats {
		statusName := getStatusName(stat.IsActive)
		fmt.Printf("  状态 %d (%s): %d 个用户\n", stat.IsActive, statusName, stat.Count)
	}

	// 2. 检查管理员用户状态
	fmt.Println("\n👑 管理员用户状态:")
	var adminUsers []User
	err = db.Where("role IN ?", []string{"admin", "manager"}).Find(&adminUsers).Error
	if err != nil {
		log.Fatal("查询管理员用户失败:", err)
	}

	for _, user := range adminUsers {
		statusName := getStatusName(user.IsActive)
		fmt.Printf("  %s (%s): %s\n", user.Nickname, user.Role, statusName)
	}

	// 3. 检查普通用户状态
	fmt.Println("\n👤 普通用户状态 (前5个):")
	var normalUsers []User
	err = db.Where("role = ?", "user").Limit(5).Find(&normalUsers).Error
	if err != nil {
		log.Fatal("查询普通用户失败:", err)
	}

	if len(normalUsers) == 0 {
		fmt.Println("  暂无普通用户")
	} else {
		for _, user := range normalUsers {
			statusName := getStatusName(user.IsActive)
			fmt.Printf("  %s (%s): %s\n", user.Nickname, user.Phone, statusName)
		}
	}

	// 4. 统计信息
	fmt.Println("\n📈 统计信息:")
	var total, active, disabled, pending int64

	db.Model(&User{}).Count(&total)
	db.Model(&User{}).Where("is_active = ?", 1).Count(&active)
	db.Model(&User{}).Where("is_active = ?", 0).Count(&disabled)
	db.Model(&User{}).Where("is_active = ?", 2).Count(&pending)

	fmt.Printf("  总用户数: %d\n", total)
	fmt.Printf("  正常用户: %d\n", active)
	fmt.Printf("  禁用用户: %d\n", disabled)
	fmt.Printf("  审核中用户: %d\n", pending)

	fmt.Println("\n✅ 用户状态字段测试完成!")
}

// getStatusName 获取状态名称
func getStatusName(status int) string {
	switch status {
	case 0:
		return "禁用"
	case 1:
		return "正常"
	case 2:
		return "审核中"
	default:
		return "未知"
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
