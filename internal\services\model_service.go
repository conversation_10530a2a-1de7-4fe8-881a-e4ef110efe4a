package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"solve-go-api/internal/models"
	"strings"
	"time"

	"gorm.io/gorm"
)

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// ModelService 模型服务
type ModelService struct {
	db *gorm.DB
}

// NewModelService 创建模型服务
func NewModelService(db *gorm.DB) *ModelService {
	return &ModelService{db: db}
}

// GetModelByName 根据名称获取模型配置
func (s *ModelService) GetModelByName(name string) (*models.Model, error) {
	var model models.Model
	err := s.db.Where("model_name = ?", name).First(&model).Error
	if err != nil {
		return nil, fmt.Errorf("model not found: %s", name)
	}
	return &model, nil
}

// CallOCRModel 调用OCR模型
func (s *ModelService) CallOCRModel(modelName, imageURL string) (*models.OCRResponse, error) {
	model, err := s.GetModelByName(modelName)
	if err != nil {
		return nil, err
	}

	if model.ModelType != "OCR" {
		return nil, fmt.Errorf("model %s is not an OCR model", modelName)
	}

	// 构建请求体
	requestBody := map[string]interface{}{
		"model": model.ModelName,
		"input": map[string]interface{}{
			"messages": []map[string]interface{}{
				{
					"role":    "system",
					"content": model.RoleSystem,
				},
				{
					"role": "user",
					"content": []map[string]interface{}{
						{
							"image": imageURL,
						},
						{
							"text": model.RoleUser,
						},
					},
				},
			},
		},
		"parameters": map[string]interface{}{
			"temperature":        model.Temperature,
			"top_p":              model.TopP,
			"top_k":              model.TopK,
			"repetition_penalty": model.RepetitionPenalty,
			"presence_penalty":   model.PresencePenalty,
			"result_format":      model.ResponseFormat,
		},
	}

	return s.callModel(model, requestBody)
}

// CallSolveModel 调用Solve模型
func (s *ModelService) CallSolveModel(modelName string, questionText string) (*models.SolveResponse, error) {
	model, err := s.GetModelByName(modelName)
	if err != nil {
		return nil, err
	}

	if model.ModelType != "solve" {
		return nil, fmt.Errorf("model %s is not a solve model", modelName)
	}

	var requestBody map[string]interface{}

	// 根据模型类型构建不同的请求体
	if strings.Contains(model.ModelName, "qwen") {
		// Qwen模型请求格式
		requestBody = map[string]interface{}{
			"model": model.ModelName,
			"input": map[string]interface{}{
				"messages": []map[string]interface{}{
					{
						"role":    "system",
						"content": model.RoleSystem,
					},
					{
						"role":    "user",
						"content": fmt.Sprintf("%s\n\n题目内容：%s", model.RoleUser, questionText),
					},
				},
			},
			"parameters": map[string]interface{}{
				"temperature":        model.Temperature,
				"top_p":              model.TopP,
				"top_k":              model.TopK,
				"repetition_penalty": model.RepetitionPenalty,
				"presence_penalty":   model.PresencePenalty,
				"result_format":      model.ResponseFormat,
			},
		}
	} else if strings.Contains(model.ModelName, "deepseek") {
		// DeepSeek模型请求格式
		requestBody = map[string]interface{}{
			"model": model.ModelName,
			"messages": []map[string]interface{}{
				{
					"role":    "system",
					"content": model.RoleSystem,
				},
				{
					"role":    "user",
					"content": fmt.Sprintf("%s\n\n题目内容：%s", model.RoleUser, questionText),
				},
			},
			"temperature":       model.Temperature,
			"top_p":             model.TopP,
			"max_tokens":        2048,
			"frequency_penalty": model.RepetitionPenalty, // 映射repetition_penalty到frequency_penalty
			"presence_penalty":  model.PresencePenalty,
			"response_format":   map[string]string{"type": model.ResponseFormat},
		}
	} else {
		return nil, fmt.Errorf("unsupported model type: %s", model.ModelName)
	}

	response, err := s.callModel(model, requestBody)
	if err != nil {
		return nil, err
	}

	// 🔍 调试日志：检查原始响应
	log.Printf("🔍 [SOLVE-DEBUG] 原始响应Choices数量: %d", len(response.Output.Choices))
	log.Printf("🔍 [SOLVE-DEBUG] 原始响应Usage: %+v", response.Usage)
	if len(response.Output.Choices) > 0 {
		log.Printf("🔍 [SOLVE-DEBUG] 第一个Choice内容类型: %T", response.Output.Choices[0].Message.Content)
		log.Printf("🔍 [SOLVE-DEBUG] 第一个Choice内容: %v", response.Output.Choices[0].Message.Content)
	} else {
		log.Printf("🔍 [SOLVE-DEBUG] Choices为空，可能是模型响应格式问题")
	}

	// 转换为SolveResponse
	solveResponse := &models.SolveResponse{
		Output: struct {
			Choices []struct {
				Message struct {
					Content string `json:"content"`
				} `json:"message"`
			} `json:"choices"`
		}{
			Choices: make([]struct {
				Message struct {
					Content string `json:"content"`
				} `json:"message"`
			}, len(response.Output.Choices)),
		},
		Usage: response.Usage,
	}

	// 复制choices数据，处理content类型转换
	for i, choice := range response.Output.Choices {
		var contentStr string
		switch v := choice.Message.Content.(type) {
		case string:
			contentStr = v
		default:
			// 对于solve模型，如果不是字符串，转换为JSON字符串
			if jsonBytes, err := json.Marshal(v); err == nil {
				contentStr = string(jsonBytes)
			} else {
				contentStr = fmt.Sprintf("%v", v)
			}
		}
		solveResponse.Output.Choices[i].Message.Content = contentStr
	}

	return solveResponse, nil
}

// callModel 通用模型调用方法
func (s *ModelService) callModel(model *models.Model, requestBody map[string]interface{}) (*models.OCRResponse, error) {
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// 🔍 记录完整的请求体日志
	log.Printf("🔍 [HTTP-REQUEST] 模型: %s", model.ModelName)
	log.Printf("🔍 [HTTP-REQUEST] 请求URL: %s", model.ModelURL)
	log.Printf("🔍 [HTTP-REQUEST] 请求体长度: %d bytes", len(jsonData))

	// 记录完整请求体（格式化输出）
	var prettyJSON bytes.Buffer
	if err := json.Indent(&prettyJSON, jsonData, "", "  "); err == nil {
		log.Printf("🔍 [HTTP-REQUEST] 完整请求体:\n%s", prettyJSON.String())
	} else {
		log.Printf("🔍 [HTTP-REQUEST] 原始请求体: %s", string(jsonData))
	}

	// 创建HTTP请求
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "POST", model.ModelURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+model.ModelKey)

	// 记录请求头信息（隐藏敏感信息）
	log.Printf("🔍 [HTTP-REQUEST] 请求头 Content-Type: %s", req.Header.Get("Content-Type"))
	log.Printf("🔍 [HTTP-REQUEST] 请求头 Authorization: Bearer %s***", model.ModelKey[:min(10, len(model.ModelKey))])

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 🔍 调试日志：记录原始响应
	log.Printf("🔍 [HTTP-RESPONSE] 响应状态码: %d", resp.StatusCode)
	log.Printf("🔍 [HTTP-RESPONSE] 响应体长度: %d bytes", len(body))
	log.Printf("🔍 [HTTP-RESPONSE] 响应头 Content-Type: %s", resp.Header.Get("Content-Type"))

	// 记录完整响应体（格式化输出）
	var prettyResponse bytes.Buffer
	if err := json.Indent(&prettyResponse, body, "", "  "); err == nil {
		log.Printf("🔍 [HTTP-RESPONSE] 完整响应体:\n%s", prettyResponse.String())
	} else {
		log.Printf("🔍 [HTTP-RESPONSE] 原始响应体: %s", string(body))
	}

	// 解析响应
	var response models.OCRResponse
	if err := json.Unmarshal(body, &response); err != nil {
		log.Printf("❌ [HTTP-RESPONSE] JSON解析失败: %v", err)
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	log.Printf("✅ [HTTP-RESPONSE] JSON解析成功")

	// 🔧 处理不同模型的特殊响应格式
	if len(response.Output.Choices) == 0 {
		log.Printf("🔍 [HTTP-DEBUG] Choices为空，尝试处理特殊模型格式")

		// 尝试解析为通用的map格式
		var rawResponse map[string]interface{}
		if err := json.Unmarshal(body, &rawResponse); err == nil {
			// 处理DeepSeek模型的响应格式
			if choices, ok := rawResponse["choices"].([]interface{}); ok && len(choices) > 0 {
				log.Printf("🔍 [HTTP-DEBUG] 检测到DeepSeek格式响应，choices数量: %d", len(choices))

				// 转换DeepSeek格式到标准格式
				for _, choice := range choices {
					if choiceMap, ok := choice.(map[string]interface{}); ok {
						if message, ok := choiceMap["message"].(map[string]interface{}); ok {
							if content, ok := message["content"].(string); ok {
								log.Printf("🔍 [HTTP-DEBUG] 从DeepSeek choices提取内容: %s", content[:min(100, len(content))])

								// 创建标准格式的Choice
								response.Output.Choices = append(response.Output.Choices, struct {
									Message struct {
										Content interface{} `json:"content"`
									} `json:"message"`
								}{
									Message: struct {
										Content interface{} `json:"content"`
									}{
										Content: content,
									},
								})
							}
						}
					}
				}
				log.Printf("🔍 [HTTP-DEBUG] DeepSeek格式转换完成，转换后choices数量: %d", len(response.Output.Choices))
			} else if output, ok := rawResponse["output"].(map[string]interface{}); ok {
				// 处理Qwen模型的响应格式
				if text, ok := output["text"].(string); ok {
					log.Printf("🔍 [HTTP-DEBUG] 从Qwen output.text提取内容: %s", text[:min(100, len(text))])

					// 创建标准格式的Choice
					response.Output.Choices = []struct {
						Message struct {
							Content interface{} `json:"content"`
						} `json:"message"`
					}{
						{
							Message: struct {
								Content interface{} `json:"content"`
							}{
								Content: text,
							},
						},
					}
					log.Printf("🔍 [HTTP-DEBUG] Qwen格式转换完成")
				}
			}
		}
	}

	return &response, nil
}
