#!/bin/bash

# 重置特定管理员密码脚本
# 目标账号: 13800000001
# 密码: 123456

set -e

# 配置
TARGET_PHONE="13800000001"
NEW_PASSWORD="123456"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 检查Go环境
check_go_env() {
    if ! command -v go &> /dev/null; then
        log_error "Go环境未安装或未在PATH中"
        exit 1
    fi
    
    if [ ! -f "go.mod" ]; then
        log_error "当前目录不是Go项目根目录"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
重置特定管理员密码脚本

用法: $0 [选项]

选项:
  --phone=PHONE     指定要重置的手机号 (默认: 13800000001)
  --password=PASS   指定新密码 (默认: 123456)
  --sql-only        仅显示SQL命令，不执行
  --test            重置后测试登录
  --help            显示帮助信息

示例:
  $0                                    # 重置默认账号密码
  $0 --phone=13800000002               # 重置指定账号密码
  $0 --password=newpass123             # 使用自定义密码
  $0 --test                            # 重置后测试登录
  $0 --sql-only                        # 仅显示SQL命令

环境变量:
  DB_HOST           数据库主机 (默认: *********** - 远程服务器)
  DB_PORT           数据库端口 (默认: 3380 - 远程端口)
  DB_USERNAME       数据库用户 (默认: gmdns - 远程用户)
  DB_PASSWORD       数据库密码 (默认: Suyan15913.. - 远程密码)
  DB_DATABASE       数据库名称 (默认: solve_web)
EOF
}

# 解析命令行参数
parse_args() {
    SQL_ONLY=false
    TEST_LOGIN=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --phone=*)
                TARGET_PHONE="${1#*=}"
                shift
                ;;
            --password=*)
                NEW_PASSWORD="${1#*=}"
                shift
                ;;
            --sql-only)
                SQL_ONLY=true
                shift
                ;;
            --test)
                TEST_LOGIN=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 生成密码哈希
generate_password_hash() {
    log_info "生成密码哈希..."
    
    # 创建临时Go文件
    local temp_file=$(mktemp /tmp/gen_hash_XXXXXX.go)
    cat > "$temp_file" << EOF
package main

import (
    "fmt"
    "log"
    "golang.org/x/crypto/bcrypt"
)

func main() {
    password := "$NEW_PASSWORD"
    hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
    if err != nil {
        log.Fatalf("生成密码哈希失败: %v", err)
    }
    fmt.Print(string(hashedPassword))
}
EOF
    
    # 生成哈希
    local hash=$(cd $(dirname "$temp_file") && go run $(basename "$temp_file") 2>/dev/null)
    rm -f "$temp_file"
    
    if [ -z "$hash" ]; then
        log_error "密码哈希生成失败"
        exit 1
    fi
    
    echo "$hash"
}

# 显示SQL命令
show_sql_commands() {
    local password_hash="$1"
    
    cat << EOF

=== SQL命令 ===

-- 查看当前用户
SELECT id, phone, role, nickname, is_active FROM hook_user WHERE phone = '$TARGET_PHONE';

-- 重置密码（如果用户存在）
UPDATE hook_user 
SET password = '$password_hash', 
    role = 'admin', 
    is_active = 1, 
    updated_at = NOW() 
WHERE phone = '$TARGET_PHONE';

-- 创建用户（如果用户不存在）
INSERT IGNORE INTO hook_user (phone, password, role, nickname, balance, is_active, created_at, updated_at)
VALUES ('$TARGET_PHONE', '$password_hash', 'admin', '超级管理员', 0, 1, NOW(), NOW());

-- 验证结果
SELECT id, phone, role, nickname, is_active FROM hook_user WHERE phone = '$TARGET_PHONE';

EOF
}

# 执行密码重置
execute_reset() {
    log_info "开始重置管理员密码..."
    log_info "目标账号: $TARGET_PHONE"
    log_info "新密码: $NEW_PASSWORD"
    
    # 生成密码哈希
    local password_hash=$(generate_password_hash)
    log_success "密码哈希生成成功"
    
    if [ "$SQL_ONLY" = true ]; then
        log_info "仅显示SQL命令模式"
        show_sql_commands "$password_hash"
        return 0
    fi
    
    # 设置环境变量
    export TARGET_PHONE="$TARGET_PHONE"
    export NEW_PASSWORD="$NEW_PASSWORD"
    export PASSWORD_HASH="$password_hash"
    
    # 执行Go程序
    if [ -f "scripts/reset_specific_admin_password.go" ]; then
        log_info "使用Go程序重置密码..."
        cd scripts
        go run reset_specific_admin_password.go
        cd ..
    else
        log_error "Go程序文件不存在: scripts/reset_specific_admin_password.go"
        log_info "显示SQL命令供手动执行:"
        show_sql_commands "$password_hash"
        return 1
    fi
}

# 测试登录
test_login() {
    if [ "$TEST_LOGIN" = false ]; then
        return 0
    fi
    
    log_info "测试登录功能..."
    
    # 检查服务是否运行
    if ! curl -s "http://localhost:8080/health" > /dev/null 2>&1; then
        log_warning "API服务未运行，跳过登录测试"
        log_info "请先启动服务: ./scripts/quick_restart.sh"
        return 0
    fi
    
    # 测试登录
    local response=$(curl -s -X POST "http://localhost:8080/api/v1/login" \
        -H "Content-Type: application/json" \
        -d "{\"phone\": \"$TARGET_PHONE\", \"password\": \"$NEW_PASSWORD\"}" \
        -w "HTTPSTATUS:%{http_code}")
    
    local http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    local response_body=$(echo "$response" | sed -E 's/HTTPSTATUS:[0-9]*$//')
    
    if [ "$http_code" = "200" ] && echo "$response_body" | grep -q '"code":200'; then
        log_success "登录测试成功"
        local token=$(echo "$response_body" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        log_info "获取到Token: ${token:0:20}..."
    else
        log_error "登录测试失败"
        log_error "HTTP状态码: $http_code"
        log_error "响应内容: $response_body"
    fi
}

# 主函数
main() {
    echo "🔐 重置特定管理员密码"
    echo "================================"
    
    parse_args "$@"
    
    if [ "$SQL_ONLY" = false ]; then
        check_go_env
    fi
    
    execute_reset
    
    if [ $? -eq 0 ]; then
        log_success "密码重置完成"
        
        echo ""
        echo "📋 登录信息:"
        echo "   手机号: $TARGET_PHONE"
        echo "   密码: $NEW_PASSWORD"
        echo "   角色: admin"
        
        echo ""
        echo "🧪 测试命令:"
        echo "   curl -X POST http://localhost:8080/api/v1/login \\"
        echo "     -H \"Content-Type: application/json\" \\"
        echo "     -d '{\"phone\": \"$TARGET_PHONE\", \"password\": \"$NEW_PASSWORD\"}'"
        
        test_login
    else
        log_error "密码重置失败"
        exit 1
    fi
    
    echo "================================"
    log_success "操作完成"
}

# 执行主函数
main "$@"
