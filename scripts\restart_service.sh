#!/bin/bash

# Go服务重启脚本
# 功能：编译、停止、启动、重启Go服务

set -e  # 遇到错误立即退出

# 配置变量
SERVICE_NAME="solve-go-api"
BINARY_NAME="solve-api"
MAIN_FILE="main.go"
PID_FILE="/tmp/${SERVICE_NAME}.pid"
LOG_FILE="logs/service.log"
BUILD_DIR="build"
PORT=${PORT:-8080}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 显示帮助信息
show_help() {
    echo "Go服务管理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start       启动服务"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  status      查看服务状态"
    echo "  build       编译服务"
    echo "  rebuild     重新编译并重启服务"
    echo "  force-stop  强制停止服务"
    echo "  logs        查看服务日志"
    echo "  clean       清理编译文件"
    echo "  help        显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  PORT        服务端口 (默认: 8080)"
    echo "  GO_ENV      运行环境 (dev/prod)"
    echo ""
    echo "示例:"
    echo "  $0 restart              # 重启服务"
    echo "  PORT=9090 $0 start      # 在9090端口启动服务"
    echo "  $0 rebuild              # 重新编译并重启"
}

# 检查Go环境
check_go_env() {
    if ! command -v go &> /dev/null; then
        log_error "Go环境未安装或未在PATH中"
        exit 1
    fi
    
    if [ ! -f "go.mod" ]; then
        log_error "当前目录不是Go项目根目录"
        exit 1
    fi
    
    if [ ! -f "$MAIN_FILE" ]; then
        log_error "找不到主文件: $MAIN_FILE"
        exit 1
    fi
}

# 创建必要的目录
create_dirs() {
    mkdir -p logs
    mkdir -p $BUILD_DIR
}

# 获取服务PID
get_pid() {
    if [ -f "$PID_FILE" ]; then
        cat "$PID_FILE"
    else
        # 尝试通过端口查找进程
        lsof -ti:$PORT 2>/dev/null || echo ""
    fi
}

# 检查服务状态
is_running() {
    local pid=$(get_pid)
    if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 编译服务
build_service() {
    log_info "开始编译服务..."
    
    # 清理旧的编译文件
    rm -f $BUILD_DIR/$BINARY_NAME
    
    # 设置编译参数
    local build_time=$(date -u '+%Y-%m-%d_%H:%M:%S')
    local git_commit=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    local go_version=$(go version | awk '{print $3}')
    
    # 编译
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
        -ldflags "-X main.BuildTime=$build_time -X main.GitCommit=$git_commit -X main.GoVersion=$go_version" \
        -o $BUILD_DIR/$BINARY_NAME $MAIN_FILE
    
    if [ $? -eq 0 ]; then
        log_success "编译完成: $BUILD_DIR/$BINARY_NAME"
        log_info "编译信息: 时间=$build_time, 提交=$git_commit, Go版本=$go_version"
    else
        log_error "编译失败"
        exit 1
    fi
}

# 启动服务
start_service() {
    if is_running; then
        log_warning "服务已在运行中 (PID: $(get_pid))"
        return 0
    fi
    
    log_info "启动服务..."
    
    # 检查二进制文件是否存在
    if [ ! -f "$BUILD_DIR/$BINARY_NAME" ]; then
        log_warning "二进制文件不存在，开始编译..."
        build_service
    fi
    
    # 启动服务
    nohup ./$BUILD_DIR/$BINARY_NAME > $LOG_FILE 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > $PID_FILE
    
    # 等待服务启动
    sleep 2
    
    if is_running; then
        log_success "服务启动成功 (PID: $pid, PORT: $PORT)"
        
        # 检查服务健康状态
        if command -v curl &> /dev/null; then
            sleep 1
            if curl -s "http://localhost:$PORT/health" > /dev/null 2>&1; then
                log_success "服务健康检查通过"
            else
                log_warning "服务健康检查失败，请检查日志"
            fi
        fi
    else
        log_error "服务启动失败"
        rm -f $PID_FILE
        exit 1
    fi
}

# 停止服务
stop_service() {
    local pid=$(get_pid)
    
    if [ -z "$pid" ]; then
        log_warning "服务未运行"
        rm -f $PID_FILE
        return 0
    fi
    
    log_info "停止服务 (PID: $pid)..."
    
    # 优雅停止
    kill -TERM $pid 2>/dev/null
    
    # 等待服务停止
    local count=0
    while [ $count -lt 10 ]; do
        if ! kill -0 $pid 2>/dev/null; then
            break
        fi
        sleep 1
        count=$((count + 1))
    done
    
    # 检查是否已停止
    if kill -0 $pid 2>/dev/null; then
        log_warning "优雅停止失败，强制终止..."
        kill -KILL $pid 2>/dev/null
        sleep 1
    fi
    
    # 清理PID文件
    rm -f $PID_FILE
    
    if ! kill -0 $pid 2>/dev/null; then
        log_success "服务已停止"
    else
        log_error "服务停止失败"
        exit 1
    fi
}

# 强制停止服务
force_stop_service() {
    local pid=$(get_pid)
    
    if [ -z "$pid" ]; then
        log_warning "服务未运行"
        rm -f $PID_FILE
        return 0
    fi
    
    log_info "强制停止服务 (PID: $pid)..."
    
    # 强制终止
    kill -KILL $pid 2>/dev/null
    
    # 清理PID文件
    rm -f $PID_FILE
    
    # 清理端口占用
    local port_pids=$(lsof -ti:$PORT 2>/dev/null || echo "")
    if [ -n "$port_pids" ]; then
        echo "$port_pids" | xargs kill -KILL 2>/dev/null || true
    fi
    
    log_success "服务已强制停止"
}

# 查看服务状态
show_status() {
    local pid=$(get_pid)
    
    echo "=== 服务状态 ==="
    echo "服务名称: $SERVICE_NAME"
    echo "二进制文件: $BUILD_DIR/$BINARY_NAME"
    echo "端口: $PORT"
    echo "PID文件: $PID_FILE"
    echo "日志文件: $LOG_FILE"
    
    if [ -n "$pid" ] && kill -0 $pid 2>/dev/null; then
        echo -e "状态: ${GREEN}运行中${NC} (PID: $pid)"
        
        # 显示进程信息
        if command -v ps &> /dev/null; then
            echo "进程信息:"
            ps -p $pid -o pid,ppid,cmd,etime,pcpu,pmem 2>/dev/null || true
        fi
        
        # 显示端口信息
        if command -v netstat &> /dev/null; then
            echo "端口信息:"
            netstat -tlnp 2>/dev/null | grep ":$PORT " || true
        fi
    else
        echo -e "状态: ${RED}未运行${NC}"
    fi
    
    # 显示二进制文件信息
    if [ -f "$BUILD_DIR/$BINARY_NAME" ]; then
        echo "二进制文件: 存在 ($(ls -lh $BUILD_DIR/$BINARY_NAME | awk '{print $5, $6, $7, $8}'))"
    else
        echo "二进制文件: 不存在"
    fi
}

# 查看日志
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        log_info "显示服务日志 (最后50行):"
        tail -n 50 "$LOG_FILE"
    else
        log_warning "日志文件不存在: $LOG_FILE"
    fi
}

# 清理编译文件
clean_build() {
    log_info "清理编译文件..."
    rm -rf $BUILD_DIR
    rm -f $PID_FILE
    log_success "清理完成"
}

# 重启服务
restart_service() {
    log_info "重启服务..."
    stop_service
    sleep 1
    start_service
}

# 重新编译并重启
rebuild_service() {
    log_info "重新编译并重启服务..."
    stop_service
    build_service
    start_service
}

# 主函数
main() {
    # 检查参数
    if [ $# -eq 0 ]; then
        show_help
        exit 1
    fi
    
    # 检查Go环境
    check_go_env
    
    # 创建必要目录
    create_dirs
    
    # 执行命令
    case "$1" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            show_status
            ;;
        build)
            build_service
            ;;
        rebuild)
            rebuild_service
            ;;
        force-stop)
            force_stop_service
            ;;
        logs)
            show_logs
            ;;
        clean)
            clean_build
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
