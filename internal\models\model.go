package models

import (
	"encoding/json"
	"time"
)

// Model AI模型配置
type Model struct {
	ID                uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	ModelName         string    `json:"model_name" gorm:"type:varchar(100);uniqueIndex;not null;comment:模型名称"`
	ModelURL          string    `json:"model_url" gorm:"type:varchar(255);not null;comment:模型请求地址"`
	ModelKey          string    `json:"model_key" gorm:"type:varchar(255);not null;comment:模型调用凭证"`
	ModelType         string    `json:"model_type" gorm:"type:enum('OCR','solve');not null;comment:模型类型"`
	RoleSystem        string    `json:"role_system" gorm:"type:text;comment:system角色内容"`
	RoleUser          string    `json:"role_user" gorm:"type:text;comment:user角色内容"`
	Temperature       float64   `json:"temperature" gorm:"type:decimal(3,2);default:0.7;comment:温度参数"`
	TopP              float64   `json:"top_p" gorm:"type:decimal(3,2);default:0.9;comment:TopP参数"`
	TopK              int       `json:"top_k" gorm:"default:50;comment:TopK参数"`
	RepetitionPenalty float64   `json:"repetition_penalty" gorm:"type:decimal(3,2);default:1.0;comment:重复惩罚"`
	PresencePenalty   float64   `json:"presence_penalty" gorm:"type:decimal(3,2);default:0.0;comment:存在惩罚"`
	ResponseFormat    string    `json:"response_format" gorm:"type:varchar(50);default:'json';comment:返回格式"`
	CreatedAt         time.Time `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt         time.Time `json:"updated_at" gorm:"comment:更新时间"`
}

// TableName 指定表名
func (Model) TableName() string {
	return "hook_models"
}

// QuestionBank 题库
type QuestionBank struct {
	ID           uint            `json:"id" gorm:"primaryKey;autoIncrement"`
	HashKey      string          `json:"hash_key" gorm:"type:varchar(32);index;not null;comment:哈希缓存键名"`
	Type         int             `json:"type" gorm:"not null;comment:问题类型 1判断题 2单选题 3多选题"`
	Content      string          `json:"content" gorm:"type:text;not null;comment:题干内容"`
	ContentClean string          `json:"content_clean" gorm:"type:text;not null;comment:清洗后的题干内容"`
	Options      json.RawMessage `json:"options" gorm:"type:json;comment:问题选项"`
	Answer       json.RawMessage `json:"answer" gorm:"type:json;comment:问题答案"`
	Analysis     string          `json:"analysis" gorm:"type:text;comment:问题解析"`
	ImageURL     string          `json:"image_url" gorm:"type:varchar(500);comment:题干图片"`
	UserURL      string          `json:"user_url" gorm:"type:varchar(500);comment:用户图片"`
	Verified     bool            `json:"verified" gorm:"default:false;comment:验证状态"`
	QuestionLen  int             `json:"question_len" gorm:"not null;comment:字符串长度"`
	CreatedAt    time.Time       `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt    time.Time       `json:"updated_at" gorm:"comment:更新时间"`
}

// TableName 指定表名
func (QuestionBank) TableName() string {
	return "hook_question_bank"
}

// SolveLog 请求日志
type SolveLog struct {
	ID        uint            `json:"id" gorm:"primaryKey;autoIncrement"`
	AppID     string          `json:"app_id" gorm:"type:varchar(16);index;not null;comment:应用ID"`
	UserURL   string          `json:"user_url" gorm:"type:varchar(500);not null;comment:用户请求图片"`
	MatchedID uint            `json:"matched_id" gorm:"default:0;comment:命中的问题ID"`
	OCRToken  int             `json:"ocr_token" gorm:"default:0;comment:OCR模型消耗token"`
	Source    string          `json:"source" gorm:"type:varchar(20);comment:响应来源"`
	Data      json.RawMessage `json:"data" gorm:"type:json;comment:响应内容"`
	Status    int             `json:"status" gorm:"not null;comment:响应状态 1成功 0失败"`
	Latency   int64           `json:"latency" gorm:"not null;comment:响应耗时(毫秒)"`
	CreatedAt time.Time       `json:"created_at" gorm:"comment:请求时间"`
}

// TableName 指定表名
func (SolveLog) TableName() string {
	return "hook_solve_logs"
}
