#!/bin/bash

# 测试管理员登录脚本
echo "🧪 开始测试管理员登录..."

# API基础URL
API_BASE_URL=${API_BASE_URL:-"http://localhost:8080/api/v1"}

# 测试账户列表
declare -a ACCOUNTS=(
    "***********:123456:超级管理员:admin"
    "***********:123456:题库管理员1:manager"
    "***********:123456:题库管理员2:manager"
)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "📊 测试配置:"
echo "   API地址: $API_BASE_URL"
echo "   测试账户数量: ${#ACCOUNTS[@]}"
echo ""

# 测试服务器是否可访问
echo "🔍 检查服务器状态..."
if curl -s "$API_BASE_URL/../health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务器运行正常${NC}"
else
    echo -e "${RED}❌ 服务器无法访问，请确保API服务正在运行${NC}"
    echo "   启动命令: go run main.go"
    exit 1
fi

echo ""

# 测试每个账户
SUCCESS_COUNT=0
TOTAL_COUNT=${#ACCOUNTS[@]}

for account in "${ACCOUNTS[@]}"; do
    IFS=':' read -r phone password nickname role <<< "$account"
    
    echo "🔐 测试登录: $nickname ($phone)"
    
    # 发送登录请求
    response=$(curl -s -X POST "$API_BASE_URL/login" \
        -H "Content-Type: application/json" \
        -d "{\"phone\":\"$phone\",\"password\":\"$password\"}" \
        -w "HTTPSTATUS:%{http_code}")
    
    # 分离响应体和HTTP状态码
    http_code=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    response_body=$(echo "$response" | sed -E 's/HTTPSTATUS:[0-9]*$//')
    
    # 检查响应
    if [ "$http_code" = "200" ]; then
        # 解析响应JSON
        if echo "$response_body" | grep -q '"code":200'; then
            token=$(echo "$response_body" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
            user_role=$(echo "$response_body" | grep -o '"role":"[^"]*"' | cut -d'"' -f4)
            
            if [ "$user_role" = "$role" ]; then
                echo -e "   ${GREEN}✅ 登录成功${NC}"
                echo "   角色: $user_role"
                echo "   Token: ${token:0:20}..."
                ((SUCCESS_COUNT++))
            else
                echo -e "   ${YELLOW}⚠️  登录成功但角色不匹配${NC}"
                echo "   期望角色: $role"
                echo "   实际角色: $user_role"
            fi
        else
            echo -e "   ${RED}❌ 登录失败${NC}"
            echo "   响应: $response_body"
        fi
    else
        echo -e "   ${RED}❌ HTTP错误 ($http_code)${NC}"
        echo "   响应: $response_body"
    fi
    
    echo ""
done

# 显示测试结果
echo "📋 测试结果汇总:"
echo "   成功: $SUCCESS_COUNT/$TOTAL_COUNT"

if [ $SUCCESS_COUNT -eq $TOTAL_COUNT ]; then
    echo -e "${GREEN}🎉 所有管理员账户登录测试通过！${NC}"
    echo ""
    echo "📝 管理员账户信息:"
    for account in "${ACCOUNTS[@]}"; do
        IFS=':' read -r phone password nickname role <<< "$account"
        echo "   $nickname - 手机号: $phone, 密码: $password, 角色: $role"
    done
    exit 0
else
    echo -e "${RED}❌ 部分账户登录失败，请检查密码重置是否成功${NC}"
    exit 1
fi
