package handlers

import (
	"net/http"
	"solve-go-api/internal/services"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AdminHandler 管理员处理器
type AdminHandler struct {
	userService     *services.UserService
	appService      *services.AppService
	questionService *services.QuestionService
}

// NewAdminHandler 创建管理员处理器
func NewAdminHandler(userService *services.UserService, appService *services.AppService, questionService *services.QuestionService) *AdminHandler {
	return &AdminHandler{
		userService:     userService,
		appService:      appService,
		questionService: questionService,
	}
}

// RechargeRequest 充值请求
type RechargeRequest struct {
	Amount int64  `json:"amount" binding:"required,min=1"`
	Reason string `json:"reason" binding:"required"`
}

// GetUsers 获取用户列表
func (h *AdminHandler) GetUsers(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON><PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON><PERSON><PERSON>("page_size", "20"))

	// 构建过滤条件
	filters := make(map[string]interface{})
	if role := c.Query("role"); role != "" {
		filters["role"] = role
	}
	if isActive := c.Query("is_active"); isActive != "" {
		if activeInt, err := strconv.Atoi(isActive); err == nil {
			filters["is_active"] = activeInt
		}
	}
	if phone := c.Query("phone"); phone != "" {
		filters["phone"] = phone
	}
	if nickname := c.Query("nickname"); nickname != "" {
		filters["nickname"] = nickname
	}

	users, total, err := h.userService.GetUsers(page, pageSize, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取用户列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data": gin.H{
			"list":      users,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// GetUser 获取单个用户
func (h *AdminHandler) GetUser(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的用户ID",
		})
		return
	}

	user, err := h.userService.GetUserByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "用户不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    user,
	})
}

// UpdateUser 更新用户
func (h *AdminHandler) UpdateUser(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的用户ID",
		})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 管理员可以更新的字段
	allowedFields := map[string]interface{}{}
	allowedKeys := []string{"nickname", "is_active", "balance"}

	for _, key := range allowedKeys {
		if value, exists := updates[key]; exists {
			allowedFields[key] = value
		}
	}

	if len(allowedFields) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "没有可更新的字段",
		})
		return
	}

	err = h.userService.UpdateUser(uint(id), allowedFields)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新用户失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "用户更新成功",
	})
}

// ActivateUser 激活用户
func (h *AdminHandler) ActivateUser(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的用户ID",
		})
		return
	}

	err = h.userService.ActivateUser(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "激活用户失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "用户激活成功",
	})
}

// DeactivateUser 禁用用户
func (h *AdminHandler) DeactivateUser(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的用户ID",
		})
		return
	}

	err = h.userService.DeactivateUser(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "禁用用户失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "用户禁用成功",
	})
}

// GetPendingUsers 获取待审核用户列表
func (h *AdminHandler) GetPendingUsers(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	users, total, err := h.userService.GetPendingUsers(page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取待审核用户失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data": gin.H{
			"list":      users,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// ApproveUser 审核通过用户
func (h *AdminHandler) ApproveUser(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的用户ID",
		})
		return
	}

	err = h.userService.ActivateUser(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "审核用户失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "用户审核通过",
	})
}

// RejectUser 审核拒绝用户
func (h *AdminHandler) RejectUser(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的用户ID",
		})
		return
	}

	err = h.userService.DeactivateUser(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "拒绝用户失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "用户审核拒绝",
	})
}

// RechargeUser 给用户充值
func (h *AdminHandler) RechargeUser(c *gin.Context) {
	adminID, _ := c.Get("user_id")
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的用户ID",
		})
		return
	}

	var req RechargeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	err = h.appService.AddUserBalance(uint(userID), req.Amount, req.Reason, adminID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "充值失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "充值成功",
	})
}

// GetUserStats 获取用户统计
func (h *AdminHandler) GetUserStats(c *gin.Context) {
	stats, err := h.userService.GetUserStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取统计信息失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    stats,
	})
}

// GetAllApps 获取所有应用
func (h *AdminHandler) GetAllApps(c *gin.Context) {
	// TODO: 实现获取所有应用的逻辑
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "功能开发中",
	})
}

// UpdateApp 更新应用
func (h *AdminHandler) UpdateApp(c *gin.Context) {
	// TODO: 实现管理员更新应用的逻辑
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "功能开发中",
	})
}

// ActivateApp 激活应用
func (h *AdminHandler) ActivateApp(c *gin.Context) {
	// TODO: 实现激活应用的逻辑
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "功能开发中",
	})
}

// DeactivateApp 禁用应用
func (h *AdminHandler) DeactivateApp(c *gin.Context) {
	// TODO: 实现禁用应用的逻辑
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "功能开发中",
	})
}

// GetConfigs 获取系统配置
func (h *AdminHandler) GetConfigs(c *gin.Context) {
	// TODO: 实现获取系统配置的逻辑
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "功能开发中",
	})
}

// UpdateConfig 更新系统配置
func (h *AdminHandler) UpdateConfig(c *gin.Context) {
	// TODO: 实现更新系统配置的逻辑
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "功能开发中",
	})
}

// GetModels 获取模型配置
func (h *AdminHandler) GetModels(c *gin.Context) {
	// TODO: 实现获取模型配置的逻辑
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "功能开发中",
	})
}

// CreateModel 创建模型配置
func (h *AdminHandler) CreateModel(c *gin.Context) {
	// TODO: 实现创建模型配置的逻辑
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "功能开发中",
	})
}

// UpdateModel 更新模型配置
func (h *AdminHandler) UpdateModel(c *gin.Context) {
	// TODO: 实现更新模型配置的逻辑
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "功能开发中",
	})
}

// DeleteModel 删除模型配置
func (h *AdminHandler) DeleteModel(c *gin.Context) {
	// TODO: 实现删除模型配置的逻辑
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "功能开发中",
	})
}
