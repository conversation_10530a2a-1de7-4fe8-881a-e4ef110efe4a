package main

import (
	"fmt"
	"log"
	"solve-go-api/internal/config"
	"solve-go-api/internal/database"
	"solve-go-api/internal/models"
	"strings"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("配置加载失败: %v", err)
	}

	// 初始化数据库
	db, err := database.InitMySQL(cfg.Database)
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	fmt.Println("🔍 [DEBUG] 开始调试解题API相关配置")
	fmt.Println(strings.Repeat("=", 60))

	// 1. 检查用户信息
	fmt.Println("\n📋 [DEBUG] 检查用户信息")
	var user models.User
	result := db.Where("id = ?", 16).First(&user)
	if result.Error != nil {
		log.Printf("❌ 查询用户失败: %v", result.Error)
	} else {
		fmt.Printf("✅ 用户信息:\n")
		fmt.Printf("   ID: %d\n", user.ID)
		fmt.Printf("   手机: %s\n", user.Phone)
		fmt.Printf("   昵称: %s\n", user.Nickname)
		fmt.Printf("   积分: %d\n", user.Balance)
		fmt.Printf("   状态: %d (%s)\n", user.IsActive, getUserStatusText(user.IsActive))
		fmt.Printf("   角色: %s\n", user.Role)
	}

	// 2. 检查应用信息
	fmt.Println("\n📱 [DEBUG] 检查应用信息")
	var app models.App
	result = db.Where("app_id = ? AND app_secret = ?", "d0w3pyySyA6jroNT", "EPj0oSPAcUNJwEIvaUSNdYRmlKLxVMhF").First(&app)
	if result.Error != nil {
		log.Printf("❌ 查询应用失败: %v", result.Error)
	} else {
		fmt.Printf("✅ 应用信息:\n")
		fmt.Printf("   ID: %d\n", app.ID)
		fmt.Printf("   名称: %s\n", app.Name)
		fmt.Printf("   AppID: %s\n", app.AppID)
		fmt.Printf("   用户ID: %d\n", app.UserID)
		fmt.Printf("   状态: %d (%s)\n", app.Status, getAppStatusText(app.Status))
		fmt.Printf("   调用次数: %d\n", app.TotalCalls)
	}

	// 3. 检查模型配置
	fmt.Println("\n🤖 [DEBUG] 检查模型配置")
	var modelList []models.Model
	result = db.Find(&modelList)
	if result.Error != nil {
		log.Printf("❌ 查询模型失败: %v", result.Error)
	} else {
		fmt.Printf("✅ 找到 %d 个模型:\n", len(modelList))
		for _, model := range modelList {
			fmt.Printf("   - ID: %d, 名称: %s, 类型: %s\n", model.ID, model.ModelName, model.ModelType)
			fmt.Printf("     URL: %s\n", model.ModelURL)
			fmt.Printf("     Key: %s...\n", maskString(model.ModelKey, 10))
		}
	}

	// 4. 检查环境变量配置
	fmt.Println("\n⚙️ [DEBUG] 检查环境变量配置")
	fmt.Printf("✅ 环境变量:\n")
	fmt.Printf("   OCR模型: %s\n", cfg.Models.OCRModel)
	fmt.Printf("   Solve模型: %s\n", cfg.Models.SolveModel)
	fmt.Printf("   数据库主机: %s\n", cfg.Database.Host)
	fmt.Printf("   Redis主机: %s\n", cfg.Redis.Host)

	// 5. 检查OCR模型是否存在
	fmt.Println("\n🔍 [DEBUG] 检查OCR模型配置")
	var ocrModel models.Model
	result = db.Where("model_name = ? AND model_type = ?", cfg.Models.OCRModel, "OCR").First(&ocrModel)
	if result.Error != nil {
		fmt.Printf("❌ OCR模型 '%s' 不存在: %v\n", cfg.Models.OCRModel, result.Error)
		fmt.Println("💡 建议: 需要在数据库中添加OCR模型配置")
	} else {
		fmt.Printf("✅ OCR模型配置存在:\n")
		fmt.Printf("   名称: %s\n", ocrModel.ModelName)
		fmt.Printf("   URL: %s\n", ocrModel.ModelURL)
		fmt.Printf("   Key: %s...\n", maskString(ocrModel.ModelKey, 10))
	}

	// 6. 检查Solve模型是否存在
	fmt.Println("\n🤖 [DEBUG] 检查Solve模型配置")
	var solveModel models.Model
	result = db.Where("model_name = ? AND model_type = ?", cfg.Models.SolveModel, "solve").First(&solveModel)
	if result.Error != nil {
		fmt.Printf("❌ Solve模型 '%s' 不存在: %v\n", cfg.Models.SolveModel, result.Error)
		fmt.Println("💡 建议: 需要在数据库中添加Solve模型配置")
	} else {
		fmt.Printf("✅ Solve模型配置存在:\n")
		fmt.Printf("   名称: %s\n", solveModel.ModelName)
		fmt.Printf("   URL: %s\n", solveModel.ModelURL)
		fmt.Printf("   Key: %s...\n", maskString(solveModel.ModelKey, 10))
	}

	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("🎯 [DEBUG] 调试信息收集完成")
}

func getUserStatusText(status int) string {
	switch status {
	case 0:
		return "禁用"
	case 1:
		return "正常"
	case 2:
		return "审核中"
	default:
		return "未知"
	}
}

func getAppStatusText(status int) string {
	switch status {
	case 0:
		return "正常"
	case 1:
		return "禁用"
	default:
		return "未知"
	}
}

func maskString(s string, showLength int) string {
	if len(s) <= showLength {
		return s
	}
	return s[:showLength] + "***"
}
