-- 重置管理员密码SQL脚本
-- 密码: 123456
-- bcrypt哈希值: $2a$10$9O2otaLC2wXu2m6XEuwxd.EUtHeyahknUl8IK7FCXdGNL95bahgrm

USE solve_web;

-- 显示当前管理员用户
SELECT '=== 当前管理员用户 ===' as info;
SELECT id, phone, role, nickname, is_active, created_at 
FROM hook_user 
WHERE role IN ('admin', 'manager') 
ORDER BY role, id;

-- 重置所有管理员和题库管理员的密码为 123456
UPDATE hook_user
SET password = '$2a$10$9O2otaLC2wXu2m6XEuwxd.EUtHeyahknUl8IK7FCXdGNL95bahgrm',
    updated_at = NOW()
WHERE role IN ('admin', 'manager');

-- 显示更新结果
SELECT '=== 密码重置完成 ===' as info;
SELECT CONCAT('影响行数: ', ROW_COUNT()) as result;

-- 如果没有管理员用户，则创建默认管理员
INSERT IGNORE INTO hook_user (phone, password, role, nickname, balance, is_active, created_at, updated_at)
VALUES
    ('13800000001', '$2a$10$9O2otaLC2wXu2m6XEuwxd.EUtHeyahknUl8IK7FCXdGNL95bahgrm', 'admin', '超级管理员', 0, 1, NOW(), NOW()),
    ('13800000002', '$2a$10$9O2otaLC2wXu2m6XEuwxd.EUtHeyahknUl8IK7FCXdGNL95bahgrm', 'manager', '题库管理员1', 0, 1, NOW(), NOW()),
    ('13800000003', '$2a$10$9O2otaLC2wXu2m6XEuwxd.EUtHeyahknUl8IK7FCXdGNL95bahgrm', 'manager', '题库管理员2', 0, 1, NOW(), NOW());

-- 显示最终的管理员用户列表
SELECT '=== 最终管理员用户列表 ===' as info;
SELECT id, phone, role, nickname, is_active, created_at, updated_at
FROM hook_user 
WHERE role IN ('admin', 'manager') 
ORDER BY role, id;

-- 显示登录信息
SELECT '=== 登录信息 ===' as info;
SELECT 
    CASE 
        WHEN role = 'admin' THEN '超级管理员'
        WHEN role = 'manager' THEN CONCAT('题库管理员', id - 1)
        ELSE role
    END as '用户类型',
    phone as '手机号',
    '123456' as '密码',
    role as '角色',
    nickname as '昵称',
    CASE WHEN is_active = 1 THEN '已激活' ELSE '未激活' END as '状态'
FROM hook_user 
WHERE role IN ('admin', 'manager') 
ORDER BY role, id;
