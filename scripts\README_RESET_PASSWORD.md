# 管理员密码重置说明

## 概述

本目录包含了重置系统管理员和题库管理员密码的脚本和工具。

## 文件说明

### 1. `reset_admin_password.sql`
- **用途**: 直接在数据库中执行的SQL脚本
- **功能**: 重置所有管理员密码为 `123456`
- **使用方法**: 在MySQL客户端中执行此脚本

### 2. `reset_admin_password.go`
- **用途**: Go程序，连接数据库重置密码
- **功能**: 自动连接数据库并重置管理员密码
- **依赖**: 需要数据库运行

### 3. `reset_admin_password.sh`
- **用途**: Shell脚本，运行Go程序
- **功能**: 设置环境变量并执行密码重置
- **使用方法**: `./reset_admin_password.sh`

### 4. `generate_password_hash.go`
- **用途**: 生成密码的bcrypt哈希值
- **功能**: 为密码 `123456` 生成哈希值
- **使用方法**: `go run generate_password_hash.go`

## 使用方法

### 方法一：使用SQL脚本（推荐）

1. 连接到MySQL数据库：
```bash
mysql -u root -p
```

2. 执行SQL脚本：
```sql
source /path/to/scripts/reset_admin_password.sql;
```

### 方法二：使用Go程序

1. 确保远程数据库正在运行
2. 设置环境变量（可选，默认使用远程配置）：
```bash
# 注意：这些是远程数据库配置，不要使用localhost
export DB_HOST=***********        # 远程MySQL服务器
export DB_PORT=3380               # 远程MySQL端口
export DB_USERNAME=gmdns          # 远程MySQL用户名
export DB_PASSWORD=Suyan15913..   # 远程MySQL密码
export DB_DATABASE=solve_web      # 数据库名称
```

3. 运行脚本：
```bash
./scripts/reset_admin_password.sh
```

### 方法三：使用Docker Compose

如果使用Docker Compose，可以直接在容器中执行：

```bash
# 启动数据库
docker compose up -d mysql

# 在容器中执行SQL脚本
docker compose exec mysql mysql -u root -proot123456 solve_web < scripts/reset_admin_password.sql
```

## 默认管理员账户

重置后的管理员账户信息：

| 用户类型 | 手机号 | 密码 | 角色 | 昵称 |
|---------|--------|------|------|------|
| 超级管理员 | 13800000001 | 123456 | admin | 超级管理员 |
| 题库管理员1 | 13800000002 | 123456 | manager | 题库管理员1 |
| 题库管理员2 | 13800000003 | 123456 | manager | 题库管理员2 |

## 登录测试

重置密码后，可以使用以下方式测试登录：

### 使用API测试

```bash
# 注意：确保API服务正在运行在8080端口
curl -X POST http://localhost:8080/api/v1/login \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800000001",
    "password": "123456"
  }'
```

### 预期响应

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "phone": "13800000001",
      "nickname": "超级管理员",
      "role": "admin",
      "balance": 0
    }
  }
}
```

## 注意事项

1. **安全性**: 重置后请及时修改密码
2. **权限**: 确保有数据库访问权限
3. **备份**: 建议在重置前备份数据库
4. **环境**: 确认数据库连接配置正确

## 故障排除

### 数据库连接失败
- 检查数据库是否运行
- 验证连接参数（主机、端口、用户名、密码）
- 确认数据库名称正确

### 权限不足
- 确保数据库用户有UPDATE权限
- 检查表名是否正确（hook_user）

### Go程序运行失败
- 确保Go环境已安装
- 检查依赖包是否已下载：`go mod tidy`
- 验证工作目录是否正确

## 联系支持

如果遇到问题，请检查：
1. 数据库连接状态
2. 环境变量配置
3. 文件权限设置
4. Go环境配置
