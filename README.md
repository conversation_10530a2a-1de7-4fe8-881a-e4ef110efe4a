# Solve Go API

基于图片的题目搜索和解答系统，使用Go语言开发的Web API服务端。

## 功能特性

- 🔍 **图片识别**: 使用OCR模型识别图片中的题目内容
- 🧠 **智能解答**: 使用AI模型提供题目答案和解析
- 📚 **题库管理**: 支持题目的增删改查和验证
- 🚀 **高性能缓存**: Redis缓存提升响应速度
- 👥 **多角色权限**: 支持管理员、题库管理员、普通用户三种角色
- 📱 **应用管理**: 用户可创建多个应用，独立计费
- 📊 **统计分析**: 详细的调用日志和统计信息

## 技术栈

- **框架**: Gin Web Framework
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **ORM**: GORM
- **认证**: JWT
- **日志**: Logrus
- **AI模型**: 阿里云DashScope (Qwen系列)、DeepSeek

## 项目结构

```
solve-go-api/
├── main.go                 # 应用入口
├── internal/
│   ├── config/            # 配置管理
│   ├── database/          # 数据库连接和迁移
│   ├── models/            # 数据模型
│   ├── services/          # 业务逻辑服务
│   ├── handlers/          # HTTP处理器
│   ├── middleware/        # 中间件
│   ├── router/            # 路由配置
│   └── utils/             # 工具函数
├── .env.example           # 环境变量示例
└── README.md
```

## 快速开始

### 1. 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 6.0+

### 2. 安装依赖

```bash
go mod tidy
```

### 3. 配置环境变量

复制 `.env.example` 为 `.env` 并修改配置：

```bash
cp .env.example .env
```

### 4. 数据库初始化

确保MySQL服务运行，创建数据库：

```sql
CREATE DATABASE solve_api CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 5. 启动服务

```bash
go run main.go
```

服务将在 `http://localhost:8080` 启动。

## API文档

### 核心解题API

```http
POST /api/v1/solve/question
Content-Type: application/json

{
    "app_id": "your_app_id",
    "app_secret": "your_app_secret", 
    "image_url": "https://example.com/question.jpg"
}
```

### 用户认证

```http
# 注册
POST /api/v1/register

# 登录
POST /api/v1/login

# 获取用户信息
GET /api/v1/user/profile
Authorization: Bearer <token>
```

### 应用管理

```http
# 创建应用
POST /api/v1/apps
Authorization: Bearer <token>

# 获取应用列表
GET /api/v1/apps
Authorization: Bearer <token>
```

## 业务流程

### 解题流程

1. **应用认证**: 验证app_id和app_secret
2. **用户验证**: 检查用户状态和积分余额
3. **OCR识别**: 调用OCR模型识别图片中的题目
4. **缓存查询**: 检查Redis缓存是否有相同题目
5. **数据库查询**: 
   - 精确匹配：根据hash_key查询
   - 模糊匹配：根据题目类型、长度、选项匹配
6. **AI解答**: 如果未找到匹配题目，调用Solve模型生成答案
7. **结果存储**: 保存新题目到数据库并更新缓存
8. **积分扣除**: 根据token消耗扣除用户积分

### 权限体系

- **超级管理员**: 系统完整控制权限
- **题库管理员**: 题库和日志管理权限
- **普通用户**: 应用创建和管理权限

## 配置说明

### 模型配置

系统支持多种AI模型，通过数据库配置：

- **OCR模型**: qwen-vl-plus (图片识别)
- **Solve模型**: qwen-plus, deepseek-chat (题目解答)

### 缓存策略

- **Redis缓存**: 存储题目查询结果，24小时过期
- **被动回写**: MySQL作为Redis的持久化方案
- **多级查询**: Redis -> MySQL精确匹配 -> MySQL模糊匹配 -> AI生成

## 部署

### 本地开发

1. **使用Docker Compose（推荐）**

```bash
# 启动所有服务（MySQL + Redis + API + Nginx）
docker-compose up -d

# 查看日志
docker-compose logs -f api

# 停止服务
docker-compose down
```

2. **手动启动**

```bash
# 确保MySQL和Redis运行
# 配置.env文件
cp .env.example .env
# 编辑.env文件，设置数据库和Redis连接信息

# 初始化数据库
mysql -u root -p < scripts/init_db.sql

# 启动应用
./scripts/start.sh
```

### Docker部署

```bash
# 构建镜像
docker build -t solve-go-api .

# 运行容器
docker run -d \
  --name solve-api \
  -p 8080:8080 \
  --env-file .env \
  solve-go-api
```

### 生产环境配置

1. 设置 `GIN_MODE=release`
2. 配置强密码和密钥
3. 启用HTTPS
4. 配置日志轮转
5. 设置监控和告警
6. 配置模型API密钥

### 测试

运行API测试脚本：

```bash
# 确保服务运行后执行
./scripts/test_api.sh
```

## 开发指南

### 添加新的AI模型

1. 在 `hook_models` 表中添加模型配置
2. 在 `ModelService` 中实现模型调用逻辑
3. 在 `ParserService` 中添加响应解析逻辑

### 扩展API接口

1. 在 `handlers` 目录添加处理器
2. 在 `router` 中注册路由
3. 添加必要的中间件和权限控制

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
