package utils

import (
	"fmt"
	"io"
	"log"
	"os"
	"runtime"
	"strings"
	"time"
)

// CustomLogger 自定义日志记录器，支持毫秒级时间戳
type CustomLogger struct {
	output io.Writer
}

// NewCustomLogger 创建新的自定义日志记录器
func NewCustomLogger(output io.Writer) *CustomLogger {
	return &CustomLogger{output: output}
}

// Printf 格式化输出日志，包含毫秒级时间戳和文件位置
func (l *CustomLogger) Printf(format string, v ...interface{}) {
	// 获取当前时间，精确到毫秒
	now := time.Now()
	timestamp := now.Format("2006/01/02 15:04:05.000")
	
	// 获取调用者信息
	_, file, line, ok := runtime.Caller(1)
	var fileInfo string
	if ok {
		// 只保留文件名，不要完整路径
		parts := strings.Split(file, "/")
		filename := parts[len(parts)-1]
		fileInfo = fmt.Sprintf("%s:%d", filename, line)
	} else {
		fileInfo = "unknown:0"
	}
	
	// 格式化消息
	message := fmt.Sprintf(format, v...)
	
	// 输出格式: 时间戳 文件位置: 消息
	logLine := fmt.Sprintf("%s %s: %s\n", timestamp, fileInfo, message)
	
	l.output.Write([]byte(logLine))
}

// SetupPreciseLogging 设置精确的日志系统
func SetupPreciseLogging() *CustomLogger {
	// 创建logs目录
	if err := os.MkdirAll("logs", 0755); err != nil {
		log.Printf("Failed to create logs directory: %v", err)
		return nil
	}

	// 打开日志文件
	logFile, err := os.OpenFile("logs/service.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Printf("Failed to open log file: %v", err)
		return nil
	}

	// 设置日志输出到文件和终端
	multiWriter := io.MultiWriter(os.Stdout, logFile)
	
	// 创建自定义日志记录器
	customLogger := NewCustomLogger(multiWriter)
	
	// 替换标准log包的输出
	log.SetOutput(multiWriter)
	log.SetFlags(0) // 清除默认标志，我们自己处理时间戳
	
	customLogger.Printf("📝 [SYSTEM] 精确日志系统初始化完成，时间精度: 毫秒级")
	
	return customLogger
}

// LogWithPreciseTime 使用精确时间记录日志
func LogWithPreciseTime(format string, v ...interface{}) {
	// 获取当前时间，精确到毫秒
	now := time.Now()
	timestamp := now.Format("2006/01/02 15:04:05.000")
	
	// 获取调用者信息
	_, file, line, ok := runtime.Caller(1)
	var fileInfo string
	if ok {
		// 只保留文件名，不要完整路径
		parts := strings.Split(file, "/")
		filename := parts[len(parts)-1]
		fileInfo = fmt.Sprintf("%s:%d", filename, line)
	} else {
		fileInfo = "unknown:0"
	}
	
	// 格式化消息
	message := fmt.Sprintf(format, v...)
	
	// 使用标准log输出，但带有我们的时间戳
	log.Printf("%s %s: %s", timestamp, fileInfo, message)
}
