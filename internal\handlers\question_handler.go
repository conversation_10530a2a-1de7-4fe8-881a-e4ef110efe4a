package handlers

import (
	"net/http"
	"solve-go-api/internal/models"
	"solve-go-api/internal/services"
	"strconv"

	"github.com/gin-gonic/gin"
)

// QuestionHandler 题库处理器
type QuestionHandler struct {
	questionService *services.QuestionService
	parserService   *services.ParserService
}

// NewQuestionHandler 创建题库处理器
func NewQuestionHandler(questionService *services.QuestionService, parserService *services.ParserService) *QuestionHandler {
	return &QuestionHandler{
		questionService: questionService,
		parserService:   parserService,
	}
}

// GetQuestions 获取题库列表
func (h *QuestionHandler) GetQuestions(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "20"))

	// 构建过滤条件
	filters := make(map[string]interface{})
	if questionType := c.Query("type"); questionType != "" {
		if typeCode, err := strconv.Atoi(questionType); err == nil {
			filters["type"] = typeCode
		}
	}
	if verified := c.Query("verified"); verified != "" {
		if verifiedBool, err := strconv.ParseBool(verified); err == nil {
			filters["verified"] = verifiedBool
		}
	}
	if content := c.Query("content"); content != "" {
		filters["content"] = content
	}

	questions, total, err := h.questionService.GetQuestions(page, pageSize, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取题库失败: " + err.Error(),
		})
		return
	}

	// 转换为响应格式
	responses := h.parserService.ConvertToQuestionResponse(questions)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data": gin.H{
			"list":      responses,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// GetQuestion 获取单个题目
func (h *QuestionHandler) GetQuestion(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的题目ID",
		})
		return
	}

	question, err := h.questionService.GetQuestionByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "题目不存在",
		})
		return
	}

	// 转换为响应格式
	responses := h.parserService.ConvertToQuestionResponse([]models.QuestionBank{*question})

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    responses[0],
	})
}

// UpdateQuestion 更新题目
func (h *QuestionHandler) UpdateQuestion(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的题目ID",
		})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 允许更新的字段
	allowedFields := map[string]interface{}{}
	allowedKeys := []string{"content", "analysis", "image_url"}

	for _, key := range allowedKeys {
		if value, exists := updates[key]; exists {
			allowedFields[key] = value
		}
	}

	if len(allowedFields) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "没有可更新的字段",
		})
		return
	}

	err = h.questionService.UpdateQuestion(uint(id), allowedFields)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新题目失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "题目更新成功",
	})
}

// DeleteQuestion 删除题目
func (h *QuestionHandler) DeleteQuestion(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的题目ID",
		})
		return
	}

	err = h.questionService.DeleteQuestion(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除题目失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "题目删除成功",
	})
}

// VerifyQuestion 验证题目
func (h *QuestionHandler) VerifyQuestion(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的题目ID",
		})
		return
	}

	err = h.questionService.VerifyQuestion(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "验证题目失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "题目验证成功",
	})
}

// UnverifyQuestion 取消验证题目
func (h *QuestionHandler) UnverifyQuestion(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的题目ID",
		})
		return
	}

	err = h.questionService.UnverifyQuestion(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "取消验证失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "取消验证成功",
	})
}

// GetStats 获取题库统计
func (h *QuestionHandler) GetStats(c *gin.Context) {
	stats, err := h.questionService.GetQuestionStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取统计信息失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    stats,
	})
}
