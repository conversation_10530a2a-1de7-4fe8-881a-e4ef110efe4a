# 配置说明文档

## ⚠️ 重要提醒：远程数据库配置

本项目已配置为使用**远程MySQL和Redis服务器**，所有本地配置已被清理和注释，以避免配置混乱问题。

## 🔧 当前配置

### 数据库配置
- **MySQL服务器**: `***********:3380`
- **用户名**: `gmdns`
- **密码**: `Suyan15913..`
- **数据库名**: `solve_web`

### Redis配置
- **Redis服务器**: `***********:6379`
- **密码**: `Suyan15913..`
- **数据库**: `0`

## 📁 已修改的文件

### 1. 核心配置文件
- `internal/config/config.go` - 更新默认配置为远程服务器
- `.env` - 包含远程服务器配置
- `.env.example` - 示例配置文件

### 2. 脚本文件
- `scripts/reset_admin_password.go` - 数据库连接配置
- `scripts/reset_specific_admin_password.go` - 数据库连接配置
- `scripts/reset_admin_password.sh` - 环境变量默认值
- `scripts/reset_specific_admin_password.sh` - 环境变量说明
- `scripts/debug_login.go` - 数据库连接配置
- `fix_admin_password.go` - 数据库连接配置

### 3. Docker配置
- `docker-compose.yml` - 本地MySQL/Redis服务已注释，API服务配置为远程数据库

### 4. 文档文件
- `scripts/README_RESET_PASSWORD.md` - 更新配置说明
- `CONFIGURATION_NOTES.md` - 本文档

## 🚫 已清理的本地配置

### 默认值更改
| 配置项 | 旧默认值 | 新默认值 | 说明 |
|--------|----------|----------|------|
| DB_HOST | localhost | *********** | 远程MySQL服务器 |
| DB_PORT | 3306 | 3380 | 远程MySQL端口 |
| DB_USERNAME | root | gmdns | 远程MySQL用户 |
| DB_PASSWORD | "" | Suyan15913.. | 远程MySQL密码 |
| REDIS_HOST | localhost | *********** | 远程Redis服务器 |
| REDIS_PASSWORD | "" | Suyan15913.. | 远程Redis密码 |

### Docker Compose更改
- 本地MySQL容器服务已注释
- 本地Redis容器服务已注释
- API服务的depends_on已注释
- API服务环境变量更新为远程配置

## 🔍 验证配置

### 1. 检查环境变量
```bash
# 查看当前配置
cat .env

# 应该看到远程服务器配置
DB_HOST=***********
DB_PORT=3380
# ...
```

### 2. 测试数据库连接
```bash
# 使用调试脚本测试连接
cd scripts
go run debug_login.go
```

### 3. 测试服务启动
```bash
# 启动服务
./scripts/quick_restart.sh

# 检查健康状态
curl http://localhost:8080/health
```

## 🛠️ 开发环境设置

### 如需使用本地数据库（不推荐）

1. **取消注释Docker Compose中的MySQL和Redis服务**
2. **修改.env文件为本地配置**：
```bash
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_local_password
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
```

3. **启动本地服务**：
```bash
docker-compose up -d mysql redis
```

### 推荐做法
- 继续使用远程数据库配置
- 所有开发和测试都在远程环境进行
- 避免本地配置导致的环境不一致问题

## 📋 故障排除

### 常见问题

1. **连接远程数据库失败**
   - 检查网络连接
   - 确认服务器IP和端口正确
   - 验证用户名密码

2. **脚本使用错误配置**
   - 检查脚本中的默认值是否已更新
   - 确认环境变量名称正确（DB_USERNAME vs DB_USER）

3. **Docker容器启动失败**
   - 确认docker-compose.yml中的depends_on已注释
   - 检查API服务的环境变量配置

### 检查清单

- [ ] `.env`文件包含远程配置
- [ ] `internal/config/config.go`默认值为远程配置
- [ ] 所有脚本使用远程配置
- [ ] Docker Compose本地服务已注释
- [ ] 服务可以正常启动和连接数据库

## 🔐 安全注意事项

1. **密码保护**: 远程数据库密码已在代码中，注意保护
2. **网络安全**: 确保远程服务器网络安全
3. **访问控制**: 限制数据库访问IP范围
4. **备份策略**: 定期备份远程数据库

## 📞 支持

如果遇到配置问题：

1. 检查本文档的故障排除部分
2. 验证网络连接到远程服务器
3. 确认所有配置文件已正确更新
4. 测试数据库连接和服务启动

## 🔄 更新记录

- **2024-06-16**: 初始配置清理，移除所有本地配置
- 所有默认值更新为远程服务器配置
- Docker Compose本地服务注释
- 添加详细的配置说明和注释
