#!/bin/bash

# Go服务高级管理脚本
# 支持systemd服务管理、Docker部署、监控等

set -e

# 配置变量
SERVICE_NAME="solve-go-api"
BINARY_NAME="solve-api"
MAIN_FILE="main.go"
SYSTEMD_SERVICE_FILE="/etc/systemd/system/${SERVICE_NAME}.service"
DOCKER_IMAGE_NAME="solve-go-api"
DOCKER_CONTAINER_NAME="solve-go-api-container"

# 获取项目根目录
PROJECT_ROOT=$(pwd)
BUILD_DIR="$PROJECT_ROOT/build"
LOGS_DIR="$PROJECT_ROOT/logs"
CONFIG_DIR="$PROJECT_ROOT/config"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_debug() { echo -e "${PURPLE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"; }

# 显示帮助信息
show_help() {
    cat << EOF
Go服务高级管理脚本

用法: $0 [命令] [选项]

基础命令:
  build           编译服务
  start           启动服务
  stop            停止服务
  restart         重启服务
  status          查看服务状态
  logs            查看服务日志

高级命令:
  install         安装为系统服务
  uninstall       卸载系统服务
  enable          启用开机自启
  disable         禁用开机自启
  reload          重新加载服务配置

Docker命令:
  docker-build    构建Docker镜像
  docker-run      运行Docker容器
  docker-stop     停止Docker容器
  docker-logs     查看Docker日志

监控命令:
  monitor         监控服务状态
  health          健康检查
  performance     性能监控

维护命令:
  backup          备份服务
  update          更新服务
  rollback        回滚服务
  clean           清理文件

选项:
  --force         强制执行
  --verbose       详细输出
  --env=ENV       指定环境 (dev/test/prod)
  --port=PORT     指定端口
  --config=FILE   指定配置文件

示例:
  $0 build --env=prod
  $0 restart --force
  $0 install --enable
  $0 docker-run --port=8080
EOF
}

# 解析命令行参数
parse_args() {
    FORCE=false
    VERBOSE=false
    ENV="dev"
    PORT="8080"
    CONFIG_FILE=""
    ENABLE_SERVICE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                FORCE=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --env=*)
                ENV="${1#*=}"
                shift
                ;;
            --port=*)
                PORT="${1#*=}"
                shift
                ;;
            --config=*)
                CONFIG_FILE="${1#*=}"
                shift
                ;;
            --enable)
                ENABLE_SERVICE=true
                shift
                ;;
            *)
                break
                ;;
        esac
    done
    
    COMMAND="$1"
}

# 检查权限
check_permissions() {
    case "$COMMAND" in
        install|uninstall|enable|disable)
            if [[ $EUID -ne 0 ]]; then
                log_error "此命令需要root权限，请使用sudo"
                exit 1
            fi
            ;;
    esac
}

# 创建目录结构
create_directories() {
    mkdir -p "$BUILD_DIR"
    mkdir -p "$LOGS_DIR"
    mkdir -p "$CONFIG_DIR"
}

# 高级编译功能
advanced_build() {
    log_info "开始高级编译..."
    
    # 检查依赖
    log_info "检查Go依赖..."
    go mod tidy
    go mod verify
    
    # 运行测试
    if [ "$ENV" != "dev" ]; then
        log_info "运行测试..."
        go test ./... -v
    fi
    
    # 设置编译变量
    local build_time=$(date -u '+%Y-%m-%d_%H:%M:%S')
    local git_commit=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    local git_branch=$(git branch --show-current 2>/dev/null || echo "unknown")
    local go_version=$(go version | awk '{print $3}')
    local build_user=$(whoami)
    
    # 编译参数
    local ldflags="-s -w"
    ldflags="$ldflags -X main.BuildTime=$build_time"
    ldflags="$ldflags -X main.GitCommit=$git_commit"
    ldflags="$ldflags -X main.GitBranch=$git_branch"
    ldflags="$ldflags -X main.GoVersion=$go_version"
    ldflags="$ldflags -X main.BuildUser=$build_user"
    ldflags="$ldflags -X main.BuildEnv=$ENV"
    
    # 根据环境设置编译选项
    case "$ENV" in
        prod)
            CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
                -ldflags "$ldflags" \
                -trimpath \
                -o "$BUILD_DIR/$BINARY_NAME" "$MAIN_FILE"
            ;;
        *)
            go build -ldflags "$ldflags" -o "$BUILD_DIR/$BINARY_NAME" "$MAIN_FILE"
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        log_success "编译完成"
        log_info "二进制文件: $BUILD_DIR/$BINARY_NAME"
        log_info "编译信息: 环境=$ENV, 分支=$git_branch, 提交=$git_commit"
        
        # 显示文件信息
        ls -lh "$BUILD_DIR/$BINARY_NAME"
    else
        log_error "编译失败"
        exit 1
    fi
}

# 创建systemd服务文件
create_systemd_service() {
    log_info "创建systemd服务文件..."
    
    cat > "$SYSTEMD_SERVICE_FILE" << EOF
[Unit]
Description=Solve Go API Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=$PROJECT_ROOT
ExecStart=$BUILD_DIR/$BINARY_NAME
ExecReload=/bin/kill -HUP \$MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30
Restart=always
RestartSec=5
StartLimitInterval=60
StartLimitBurst=3

# 环境变量
Environment=GO_ENV=$ENV
Environment=PORT=$PORT
$([ -n "$CONFIG_FILE" ] && echo "Environment=CONFIG_FILE=$CONFIG_FILE")

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$PROJECT_ROOT/logs $PROJECT_ROOT/data

# 日志设置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

[Install]
WantedBy=multi-user.target
EOF
    
    log_success "systemd服务文件已创建: $SYSTEMD_SERVICE_FILE"
}

# 安装系统服务
install_service() {
    log_info "安装系统服务..."
    
    # 检查二进制文件
    if [ ! -f "$BUILD_DIR/$BINARY_NAME" ]; then
        log_warning "二进制文件不存在，开始编译..."
        advanced_build
    fi
    
    # 创建服务文件
    create_systemd_service
    
    # 重新加载systemd
    systemctl daemon-reload
    
    # 启用服务
    if [ "$ENABLE_SERVICE" = true ]; then
        systemctl enable "$SERVICE_NAME"
        log_success "服务已启用开机自启"
    fi
    
    log_success "系统服务安装完成"
}

# 卸载系统服务
uninstall_service() {
    log_info "卸载系统服务..."
    
    # 停止服务
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        systemctl stop "$SERVICE_NAME"
    fi
    
    # 禁用服务
    if systemctl is-enabled --quiet "$SERVICE_NAME"; then
        systemctl disable "$SERVICE_NAME"
    fi
    
    # 删除服务文件
    if [ -f "$SYSTEMD_SERVICE_FILE" ]; then
        rm -f "$SYSTEMD_SERVICE_FILE"
        systemctl daemon-reload
    fi
    
    log_success "系统服务已卸载"
}

# Docker构建
docker_build() {
    log_info "构建Docker镜像..."
    
    # 创建Dockerfile（如果不存在）
    if [ ! -f "Dockerfile" ]; then
        log_info "创建Dockerfile..."
        cat > Dockerfile << 'EOF'
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/main .
COPY --from=builder /app/config ./config

EXPOSE 8080
CMD ["./main"]
EOF
    fi
    
    # 构建镜像
    docker build -t "$DOCKER_IMAGE_NAME:latest" .
    
    if [ $? -eq 0 ]; then
        log_success "Docker镜像构建完成: $DOCKER_IMAGE_NAME:latest"
    else
        log_error "Docker镜像构建失败"
        exit 1
    fi
}

# Docker运行
docker_run() {
    log_info "运行Docker容器..."
    
    # 停止现有容器
    if docker ps -q -f name="$DOCKER_CONTAINER_NAME" | grep -q .; then
        log_info "停止现有容器..."
        docker stop "$DOCKER_CONTAINER_NAME"
        docker rm "$DOCKER_CONTAINER_NAME"
    fi
    
    # 运行新容器
    docker run -d \
        --name "$DOCKER_CONTAINER_NAME" \
        -p "$PORT:8080" \
        -v "$PROJECT_ROOT/logs:/root/logs" \
        -v "$PROJECT_ROOT/config:/root/config" \
        -e GO_ENV="$ENV" \
        --restart unless-stopped \
        "$DOCKER_IMAGE_NAME:latest"
    
    if [ $? -eq 0 ]; then
        log_success "Docker容器启动成功"
        log_info "容器名称: $DOCKER_CONTAINER_NAME"
        log_info "端口映射: $PORT:8080"
    else
        log_error "Docker容器启动失败"
        exit 1
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local health_url="http://localhost:$PORT/health"
    local max_attempts=5
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log_info "健康检查尝试 $attempt/$max_attempts..."
        
        if curl -s -f "$health_url" > /dev/null; then
            log_success "健康检查通过"
            return 0
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "健康检查失败"
    return 1
}

# 性能监控
performance_monitor() {
    log_info "性能监控..."
    
    if command -v systemctl &> /dev/null && systemctl is-active --quiet "$SERVICE_NAME"; then
        # systemd服务监控
        systemctl status "$SERVICE_NAME" --no-pager
    elif docker ps -q -f name="$DOCKER_CONTAINER_NAME" | grep -q .; then
        # Docker容器监控
        docker stats "$DOCKER_CONTAINER_NAME" --no-stream
    else
        log_warning "未找到运行中的服务"
    fi
}

# 主函数
main() {
    parse_args "$@"
    
    if [ -z "$COMMAND" ]; then
        show_help
        exit 1
    fi
    
    check_permissions
    create_directories
    
    case "$COMMAND" in
        build)
            advanced_build
            ;;
        start)
            if command -v systemctl &> /dev/null && [ -f "$SYSTEMD_SERVICE_FILE" ]; then
                systemctl start "$SERVICE_NAME"
                log_success "服务已启动"
            else
                log_error "请先安装系统服务或使用基础脚本"
            fi
            ;;
        stop)
            if command -v systemctl &> /dev/null && [ -f "$SYSTEMD_SERVICE_FILE" ]; then
                systemctl stop "$SERVICE_NAME"
                log_success "服务已停止"
            else
                log_error "请使用基础脚本停止服务"
            fi
            ;;
        restart)
            if command -v systemctl &> /dev/null && [ -f "$SYSTEMD_SERVICE_FILE" ]; then
                systemctl restart "$SERVICE_NAME"
                log_success "服务已重启"
            else
                log_error "请先安装系统服务"
            fi
            ;;
        status)
            if command -v systemctl &> /dev/null && [ -f "$SYSTEMD_SERVICE_FILE" ]; then
                systemctl status "$SERVICE_NAME" --no-pager
            else
                log_warning "系统服务未安装"
            fi
            ;;
        logs)
            if command -v systemctl &> /dev/null && [ -f "$SYSTEMD_SERVICE_FILE" ]; then
                journalctl -u "$SERVICE_NAME" -f
            else
                log_warning "系统服务未安装，显示本地日志"
                tail -f "$LOGS_DIR/service.log" 2>/dev/null || log_error "日志文件不存在"
            fi
            ;;
        install)
            install_service
            ;;
        uninstall)
            uninstall_service
            ;;
        enable)
            systemctl enable "$SERVICE_NAME"
            log_success "服务已启用开机自启"
            ;;
        disable)
            systemctl disable "$SERVICE_NAME"
            log_success "服务已禁用开机自启"
            ;;
        docker-build)
            docker_build
            ;;
        docker-run)
            docker_run
            ;;
        docker-stop)
            if docker ps -q -f name="$DOCKER_CONTAINER_NAME" | grep -q .; then
                docker stop "$DOCKER_CONTAINER_NAME"
                docker rm "$DOCKER_CONTAINER_NAME"
                log_success "Docker容器已停止"
            else
                log_warning "Docker容器未运行"
            fi
            ;;
        docker-logs)
            docker logs -f "$DOCKER_CONTAINER_NAME"
            ;;
        health)
            health_check
            ;;
        monitor)
            performance_monitor
            ;;
        clean)
            rm -rf "$BUILD_DIR"
            log_success "构建文件已清理"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
