package main

import (
	"fmt"
	"log"
	"solve-go-api/internal/config"
	"solve-go-api/internal/models"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("配置加载失败:", err)
	}

	// 连接数据库
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.Database,
		cfg.Database.Charset,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}

	fmt.Println("=== 题库统计信息 ===")

	// 1. 总题目数量
	var totalCount int64
	db.Model(&models.QuestionBank{}).Count(&totalCount)
	fmt.Printf("总题目数量: %d\n", totalCount)

	// 2. 已验证题目数量
	var verifiedCount int64
	db.Model(&models.QuestionBank{}).Where("verified = ?", true).Count(&verifiedCount)
	fmt.Printf("已验证题目数量: %d\n", verifiedCount)

	// 3. 按类型统计已验证题目
	var typeStats []struct {
		Type  int   `json:"type"`
		Count int64 `json:"count"`
	}
	db.Model(&models.QuestionBank{}).
		Select("type, COUNT(*) as count").
		Where("verified = ?", true).
		Group("type").
		Find(&typeStats)

	fmt.Println("\n=== 已验证题目按类型统计 ===")
	for _, stat := range typeStats {
		var typeName string
		switch stat.Type {
		case 1:
			typeName = "判断题"
		case 2:
			typeName = "单选题"
		case 3:
			typeName = "多选题"
		default:
			typeName = "未知类型"
		}
		fmt.Printf("类型 %d (%s): %d 题\n", stat.Type, typeName, stat.Count)
	}

	// 4. 查询单选题的题目长度分布（已验证）
	var lengthStats []struct {
		QuestionLen int   `json:"question_len"`
		Count       int64 `json:"count"`
	}
	db.Model(&models.QuestionBank{}).
		Select("question_len, COUNT(*) as count").
		Where("verified = ? AND type = ?", true, 2).
		Group("question_len").
		Order("question_len").
		Find(&lengthStats)

	fmt.Println("\n=== 已验证单选题长度分布 ===")
	for _, stat := range lengthStats {
		fmt.Printf("长度 %d: %d 题\n", stat.QuestionLen, stat.Count)
	}

	// 5. 查询长度在13-23范围内的单选题（当前查询的是18±5）
	var rangeCount int64
	db.Model(&models.QuestionBank{}).
		Where("verified = ? AND type = ? AND question_len BETWEEN ? AND ?", true, 2, 13, 23).
		Count(&rangeCount)
	fmt.Printf("\n长度在13-23范围内的已验证单选题数量: %d\n", rangeCount)

	// 6. 查看所有题目（包括未验证的）
	var allQuestions []models.QuestionBank
	db.Limit(10).Find(&allQuestions)

	fmt.Println("\n=== 所有题目样例（包括未验证） ===")
	for i, q := range allQuestions {
		fmt.Printf("样例 %d:\n", i+1)
		fmt.Printf("  ID: %d\n", q.ID)
		fmt.Printf("  类型: %d\n", q.Type)
		fmt.Printf("  内容: %s\n", q.Content)
		fmt.Printf("  清洗后内容: %s\n", q.ContentClean)
		fmt.Printf("  长度: %d\n", q.QuestionLen)
		fmt.Printf("  已验证: %t\n", q.Verified)
		fmt.Printf("  哈希键: %s\n", q.HashKey)
		fmt.Println()
	}

	// 7. 检查当前查询的具体条件
	fmt.Println("=== 当前查询条件分析 ===")
	fmt.Println("查询条件: verified = true AND type = 2 AND ABS(question_len - 18) <= 5")
	fmt.Println("即: verified = true AND type = 2 AND question_len BETWEEN 13 AND 23")

	var matchingQuestions []models.QuestionBank
	db.Where("verified = ? AND type = ? AND ABS(question_len - ?) <= 5", true, 2, 18).
		Find(&matchingQuestions)

	fmt.Printf("符合条件的题目数量: %d\n", len(matchingQuestions))

	if len(matchingQuestions) > 0 {
		fmt.Println("\n符合条件的题目:")
		for i, q := range matchingQuestions {
			if i >= 3 { // 只显示前3个
				break
			}
			fmt.Printf("  题目 %d: %s (长度: %d)\n", i+1, q.ContentClean, q.QuestionLen)
		}
	}
}
