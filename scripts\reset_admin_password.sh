#!/bin/bash

# 重置管理员密码脚本
echo "🔧 开始重置管理员密码..."

# 检查是否在项目根目录
if [ ! -f "go.mod" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 设置环境变量（使用远程数据库配置，避免本地配置问题）
# 注意：这些默认值指向远程服务器，不要使用localhost
export DB_HOST=${DB_HOST:-"***********"}        # 远程MySQL服务器
export DB_PORT=${DB_PORT:-"3380"}               # 远程MySQL端口
export DB_USERNAME=${DB_USERNAME:-"gmdns"}      # 远程MySQL用户名
export DB_PASSWORD=${DB_PASSWORD:-"Suyan15913.."}  # 远程MySQL密码
export DB_DATABASE=${DB_DATABASE:-"solve_web"}  # 数据库名称

echo "📊 数据库连接信息:"
echo "   主机: $DB_HOST:$DB_PORT"
echo "   用户: $DB_USERNAME"
echo "   数据库: $DB_DATABASE"
echo ""

# 运行重置密码程序
echo "🚀 执行密码重置..."
cd scripts
go run reset_admin_password.go

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 密码重置成功完成！"
    echo ""
    echo "📋 管理员登录信息:"
    echo "   超级管理员:"
    echo "     手机号: 13800000001"
    echo "     密码: 123456"
    echo "     角色: admin"
    echo ""
    echo "   题库管理员1:"
    echo "     手机号: 13800000002"
    echo "     密码: 123456"
    echo "     角色: manager"
    echo ""
    echo "   题库管理员2:"
    echo "     手机号: 13800000003"
    echo "     密码: 123456"
    echo "     角色: manager"
    echo ""
    echo "🔐 请使用上述账户信息登录系统"
else
    echo "❌ 密码重置失败，请检查数据库连接和配置"
    exit 1
fi
