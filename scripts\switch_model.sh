#!/bin/bash

# 模型切换脚本
# 用法: ./scripts/switch_model.sh [qwen|deepseek]

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}=== 模型切换脚本 ===${NC}"
    echo ""
    echo "用法: $0 [模型名称]"
    echo ""
    echo "支持的模型:"
    echo "  qwen      - 切换到 qwen-plus 模型"
    echo "  deepseek  - 切换到 deepseek-chat 模型"
    echo "  status    - 显示当前模型配置"
    echo ""
    echo "示例:"
    echo "  $0 qwen      # 切换到qwen模型"
    echo "  $0 deepseek  # 切换到deepseek模型"
    echo "  $0 status    # 查看当前配置"
    echo ""
}

# 显示当前配置
show_status() {
    echo -e "${BLUE}=== 当前模型配置 ===${NC}"
    
    if [ -f ".env" ]; then
        # 从.env文件读取配置
        OCR_MODEL=$(grep "^MODEL_OCR=" .env | cut -d'=' -f2)
        SOLVE_MODEL=$(grep "^MODEL_SOLVE=" .env | cut -d'=' -f2)
        
        echo -e "OCR模型:  ${GREEN}${OCR_MODEL:-qwen-vl-plus}${NC}"
        echo -e "解题模型: ${GREEN}${SOLVE_MODEL:-qwen-plus}${NC}"
        
        # 检查环境变量
        if [ -n "$MODEL_SOLVE" ]; then
            echo -e "环境变量: ${YELLOW}$MODEL_SOLVE${NC}"
            if [ "$MODEL_SOLVE" != "$SOLVE_MODEL" ]; then
                echo -e "${YELLOW}⚠️  环境变量与.env文件不一致${NC}"
            fi
        fi
    else
        echo -e "${RED}❌ .env文件不存在${NC}"
    fi
    
    echo ""
}

# 更新.env文件中的模型配置
update_env_file() {
    local model_name=$1
    
    if [ ! -f ".env" ]; then
        echo -e "${RED}❌ .env文件不存在${NC}"
        return 1
    fi
    
    # 备份原文件
    cp .env .env.backup
    
    # 更新MODEL_SOLVE配置
    if grep -q "^MODEL_SOLVE=" .env; then
        # 替换现有配置
        sed -i.tmp "s/^MODEL_SOLVE=.*/MODEL_SOLVE=$model_name/" .env
        rm -f .env.tmp
    else
        # 添加新配置
        echo "MODEL_SOLVE=$model_name" >> .env
    fi
    
    echo -e "${GREEN}✅ .env文件已更新${NC}"
}

# 切换到qwen模型
switch_to_qwen() {
    echo -e "${BLUE}=== 切换到 Qwen-Plus 模型 ===${NC}"
    
    update_env_file "qwen-plus"
    export MODEL_SOLVE=qwen-plus
    
    echo -e "${GREEN}✅ 已切换到 qwen-plus 模型${NC}"
    echo -e "${YELLOW}请重启服务使配置生效: ./start.sh${NC}"
}

# 切换到deepseek模型
switch_to_deepseek() {
    echo -e "${BLUE}=== 切换到 DeepSeek 模型 ===${NC}"
    
    update_env_file "deepseek-chat"
    export MODEL_SOLVE=deepseek-chat
    
    echo -e "${GREEN}✅ 已切换到 deepseek-chat 模型${NC}"
    echo -e "${YELLOW}⚠️  请确保数据库中有 deepseek-chat 模型配置${NC}"
    echo -e "${YELLOW}请重启服务使配置生效: ./start.sh${NC}"
}

# 主逻辑
case "$1" in
    "qwen")
        switch_to_qwen
        ;;
    "deepseek")
        switch_to_deepseek
        ;;
    "status")
        show_status
        ;;
    "help"|"-h"|"--help"|"")
        show_help
        ;;
    *)
        echo -e "${RED}❌ 未知的模型: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac

# 显示最终状态
echo ""
show_status
