#!/bin/bash

# 加载环境变量脚本
# 用法: source scripts/load_env.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 加载环境变量 ===${NC}"

# 检查 .env 文件是否存在
if [ ! -f ".env" ]; then
    echo -e "${RED}❌ .env 文件不存在${NC}"
    echo -e "${YELLOW}请复制 .env.example 为 .env 并配置相应的值${NC}"
    echo -e "${BLUE}命令: cp .env.example .env${NC}"
    return 1 2>/dev/null || exit 1
fi

echo -e "${GREEN}✅ 找到 .env 文件${NC}"

# 加载环境变量
set -a  # 自动导出所有变量
source .env
set +a  # 关闭自动导出

echo -e "${GREEN}✅ 环境变量加载完成${NC}"

# 显示关键配置
echo -e "\n${BLUE}=== 关键配置信息 ===${NC}"
echo -e "服务端口: ${GREEN}${SERVER_PORT:-8080}${NC}"
echo -e "运行模式: ${GREEN}${GIN_MODE:-debug}${NC}"
echo -e "数据库: ${GREEN}${DB_USERNAME}@${DB_HOST}:${DB_PORT}/${DB_DATABASE}${NC}"
echo -e "Redis: ${GREEN}${REDIS_HOST}:${REDIS_PORT}${NC}"
echo -e "OCR模型: ${GREEN}${MODEL_OCR:-qwen-vl-plus}${NC}"
echo -e "解题模型: ${GREEN}${MODEL_SOLVE:-qwen-plus}${NC}"

# 检查模型配置
echo -e "\n${BLUE}=== 模型配置检查 ===${NC}"
if [ "$MODEL_SOLVE" = "deepseek-chat" ]; then
    echo -e "${GREEN}✅ 当前使用 DeepSeek 模型${NC}"
    echo -e "${YELLOW}⚠️  请确保数据库中有 deepseek-chat 模型配置${NC}"
elif [ "$MODEL_SOLVE" = "qwen-plus" ]; then
    echo -e "${GREEN}✅ 当前使用 Qwen-Plus 模型${NC}"
else
    echo -e "${YELLOW}⚠️  未知的解题模型: ${MODEL_SOLVE}${NC}"
    echo -e "${BLUE}支持的模型: qwen-plus, deepseek-chat${NC}"
fi

echo -e "\n${BLUE}=== 使用说明 ===${NC}"
echo -e "1. 要切换到 DeepSeek 模型:"
echo -e "   ${BLUE}export MODEL_SOLVE=deepseek-chat${NC}"
echo -e "   ${BLUE}./start.sh${NC}"
echo -e ""
echo -e "2. 要切换到 Qwen-Plus 模型:"
echo -e "   ${BLUE}export MODEL_SOLVE=qwen-plus${NC}"
echo -e "   ${BLUE}./start.sh${NC}"
echo -e ""
echo -e "3. 永久修改配置:"
echo -e "   ${BLUE}编辑 .env 文件中的 MODEL_SOLVE 值${NC}"
echo -e "   ${BLUE}重启服务生效${NC}"

echo -e "\n${GREEN}环境变量加载完成！${NC}"
