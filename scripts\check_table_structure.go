package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 获取数据库连接信息
	host := getEnv("DB_HOST", "***********")
	port := getEnv("DB_PORT", "3380")
	user := getEnv("DB_USER", "gmdns")
	password := getEnv("DB_PASSWORD", "Suyan15913..")
	dbname := getEnv("DB_NAME", "solve_web")

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		user, password, host, port, dbname)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	fmt.Println("🔍 检查 hook_user 表结构...")

	// 查询表结构
	var columns []struct {
		Field   string  `json:"field"`
		Type    string  `json:"type"`
		Null    string  `json:"null"`
		Key     string  `json:"key"`
		Default *string `json:"default"`
		Extra   string  `json:"extra"`
	}

	err = db.Raw("SHOW COLUMNS FROM hook_user").Scan(&columns).Error
	if err != nil {
		log.Fatal("查询表结构失败:", err)
	}

	fmt.Println("\n📋 表结构信息:")
	fmt.Printf("%-15s %-20s %-8s %-8s %-10s %s\n", "字段名", "类型", "允许NULL", "键", "默认值", "额外")
	fmt.Println("-------------------------------------------------------------------------------")

	for _, col := range columns {
		defaultVal := "NULL"
		if col.Default != nil {
			defaultVal = *col.Default
		}
		fmt.Printf("%-15s %-20s %-8s %-8s %-10s %s\n",
			col.Field, col.Type, col.Null, col.Key, defaultVal, col.Extra)
	}

	// 特别检查 is_active 字段
	fmt.Println("\n🎯 is_active 字段详细信息:")
	for _, col := range columns {
		if col.Field == "is_active" {
			fmt.Printf("  字段名: %s\n", col.Field)
			fmt.Printf("  类型: %s\n", col.Type)
			fmt.Printf("  允许NULL: %s\n", col.Null)
			fmt.Printf("  默认值: %s\n", func() string {
				if col.Default != nil {
					return *col.Default
				}
				return "NULL"
			}())
			fmt.Printf("  额外信息: %s\n", col.Extra)
			break
		}
	}

	// 检查最近注册的用户
	fmt.Println("\n👤 最近注册的用户 (按创建时间倒序):")
	var recentUsers []struct {
		ID        uint   `json:"id"`
		Phone     string `json:"phone"`
		Nickname  string `json:"nickname"`
		Role      string `json:"role"`
		IsActive  int    `json:"is_active"`
		CreatedAt string `json:"created_at"`
	}

	err = db.Raw("SELECT id, phone, nickname, role, is_active, created_at FROM hook_user ORDER BY created_at DESC LIMIT 5").Scan(&recentUsers).Error
	if err != nil {
		log.Fatal("查询最近用户失败:", err)
	}

	for _, user := range recentUsers {
		statusName := getStatusName(user.IsActive)
		fmt.Printf("  ID: %d, 手机: %s, 昵称: %s, 角色: %s, 状态: %d (%s), 创建时间: %s\n",
			user.ID, user.Phone, user.Nickname, user.Role, user.IsActive, statusName, user.CreatedAt)
	}
}

// getStatusName 获取状态名称
func getStatusName(status int) string {
	switch status {
	case 0:
		return "禁用"
	case 1:
		return "正常"
	case 2:
		return "审核中"
	default:
		return "未知"
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
