package services

import (
	"encoding/json"
	"fmt"
	"log"
	"solve-go-api/internal/config"
	"solve-go-api/internal/models"
	"time"

	"gorm.io/gorm"
)

// SolveService 核心解题服务
type SolveService struct {
	db              *gorm.DB
	cfg             *config.Config
	modelService    *ModelService
	parserService   *ParserService
	cacheService    *CacheService
	questionService *QuestionService
	matchService    *MatchService
	appService      *AppService
	logService      *LogService
}

// NewSolveService 创建解题服务
func NewSolveService(
	db *gorm.DB,
	cfg *config.Config,
	modelService *ModelService,
	parserService *ParserService,
	cacheService *CacheService,
	questionService *QuestionService,
	matchService *MatchService,
	appService *AppService,
	logService *LogService,
) *SolveService {
	return &SolveService{
		db:              db,
		cfg:             cfg,
		modelService:    modelService,
		parserService:   parserService,
		cacheService:    cacheService,
		questionService: questionService,
		matchService:    matchService,
		appService:      appService,
		logService:      logService,
	}
}

// ProcessSolveRequest 处理解题请求
func (s *SolveService) ProcessSolveRequest(request *models.APIRequest) *models.ProcessContext {
	ctx := &models.ProcessContext{
		AppID:     request.AppID,
		UserURL:   request.ImageURL,
		StartTime: time.Now(),
		Status:    0,
	}

	log.Printf("🚀 [SOLVE] 开始处理解题请求 - AppID: %s, ImageURL: %s", request.AppID, request.ImageURL)

	// 1. 验证应用和用户
	log.Printf("📋 [SOLVE] 步骤1: 验证应用和用户")
	if err := s.validateAppAndUser(ctx); err != nil {
		log.Printf("❌ [SOLVE] 应用/用户验证失败: %v", err)
		ctx.SetError(err.Error())
		return ctx
	}
	log.Printf("✅ [SOLVE] 应用/用户验证成功 - UserID: %d, Balance: %d", ctx.User.ID, ctx.User.Balance)

	// 2. 检查用户积分
	log.Printf("💰 [SOLVE] 步骤2: 检查用户积分 - 当前积分: %d", ctx.User.Balance)
	if ctx.User.Balance <= 0 {
		log.Printf("❌ [SOLVE] 用户积分不足: %d", ctx.User.Balance)
		ctx.SetError("用户积分不足")
		return ctx
	}

	// 3. 调用OCR模型识别图片
	log.Printf("🔍 [SOLVE] 步骤3: 调用OCR模型识别图片")
	if err := s.processOCR(ctx); err != nil {
		log.Printf("❌ [SOLVE] OCR处理失败: %v", err)
		ctx.SetError(err.Error())
		return ctx
	}
	log.Printf("✅ [SOLVE] OCR处理成功 - Token消耗: %d, 题目类型: %s", ctx.OCRToken, ctx.SaveQuestion.Type)

	// 4. 检查Redis缓存
	log.Printf("🔄 [SOLVE] 步骤4: 检查Redis缓存")
	if s.checkRedisCache(ctx) {
		log.Printf("✅ [SOLVE] Redis缓存命中")
		return ctx
	}
	log.Printf("⚪ [SOLVE] Redis缓存未命中")

	// 5. 检查MySQL精确匹配
	log.Printf("🎯 [SOLVE] 步骤5: 检查MySQL精确匹配")
	if s.checkMySQLExactMatch(ctx) {
		log.Printf("✅ [SOLVE] MySQL精确匹配成功")
		return ctx
	}
	log.Printf("⚪ [SOLVE] MySQL精确匹配未命中")

	// 6. 检查MySQL模糊匹配
	log.Printf("🔍 [SOLVE] 步骤6: 检查MySQL模糊匹配")
	if s.checkMySQLFuzzyMatch(ctx) {
		log.Printf("✅ [SOLVE] MySQL模糊匹配成功")
		return ctx
	}
	log.Printf("⚪ [SOLVE] MySQL模糊匹配未命中")

	// 7. 调用Solve模型解答
	log.Printf("🤖 [SOLVE] 步骤7: 调用Solve模型解答")
	if err := s.processSolve(ctx); err != nil {
		log.Printf("❌ [SOLVE] Solve模型处理失败: %v", err)
		ctx.SetError(err.Error())
		return ctx
	}
	log.Printf("✅ [SOLVE] Solve模型处理成功")

	// 8. 保存题目到数据库并回写缓存
	log.Printf("💾 [SOLVE] 步骤8: 保存题目到数据库并回写缓存")
	if err := s.saveAndCache(ctx); err != nil {
		log.Printf("❌ [SOLVE] 保存和缓存失败: %v", err)
		ctx.SetError(err.Error())
		return ctx
	}
	log.Printf("✅ [SOLVE] 保存和缓存成功")

	log.Printf("🎉 [SOLVE] 解题请求处理完成 - 总耗时: %v", time.Since(ctx.StartTime))
	return ctx
}

// validateAppAndUser 验证应用和用户
func (s *SolveService) validateAppAndUser(ctx *models.ProcessContext) error {
	app, err := s.appService.GetAppByID(ctx.AppID)
	if err != nil {
		return fmt.Errorf("应用不存在")
	}

	if app.Status != 0 {
		return fmt.Errorf("应用已被禁用")
	}

	user, err := s.appService.GetUserByID(app.UserID)
	if err != nil {
		return fmt.Errorf("用户不存在")
	}

	// 检查用户状态
	if user.IsActive != models.UserStatusActive {
		switch user.IsActive {
		case models.UserStatusDisabled:
			return fmt.Errorf("用户已被禁用")
		case models.UserStatusPending:
			return fmt.Errorf("账户正在审核中，请等待管理员审核")
		default:
			return fmt.Errorf("账户状态异常")
		}
	}

	ctx.App = app
	ctx.User = user
	return nil
}

// processOCR 处理OCR识别
func (s *SolveService) processOCR(ctx *models.ProcessContext) error {
	log.Printf("🔍 [OCR] 开始OCR处理 - 模型: %s, 图片URL: %s", s.cfg.Models.OCRModel, ctx.UserURL)

	// 调用OCR模型
	response, err := s.modelService.CallOCRModel(s.cfg.Models.OCRModel, ctx.UserURL)
	if err != nil {
		log.Printf("❌ [OCR] 模型调用失败: %v", err)
		return fmt.Errorf("OCR模型调用失败: %w", err)
	}
	log.Printf("✅ [OCR] 模型调用成功 - Token消耗: %d", response.Usage.TotalTokens)

	// 记录token消耗
	ctx.OCRToken = response.Usage.TotalTokens

	// 解析OCR响应
	log.Printf("📝 [OCR] 开始解析OCR响应")
	saveQuestion, err := s.parserService.ParseOCRResponse(response, ctx.UserURL)
	if err != nil {
		log.Printf("❌ [OCR] 响应解析失败: %v", err)
		return err
	}
	log.Printf("✅ [OCR] 响应解析成功 - 题目类型: %s, 内容长度: %d", saveQuestion.Type, len(saveQuestion.Content))

	// 更新ProcessContext并记录详细信息
	ctx.SaveQuestion = saveQuestion
	log.Printf("🔄 [CONTEXT] ProcessContext已更新")
	log.Printf("🔄 [CONTEXT] SaveQuestion.Type: %s", ctx.SaveQuestion.Type)
	log.Printf("🔄 [CONTEXT] SaveQuestion.HashKey: %s", ctx.SaveQuestion.HashKey)
	log.Printf("🔄 [CONTEXT] SaveQuestion.QuestionLen: %d", ctx.SaveQuestion.QuestionLen)
	log.Printf("🔄 [CONTEXT] SaveQuestion.UserURL: %s", ctx.SaveQuestion.UserURL)
	log.Printf("🔄 [CONTEXT] OCRToken: %d", ctx.OCRToken)
	log.Printf("🔄 [CONTEXT] 当前处理阶段: OCR解析完成，准备进入缓存检查阶段")

	return nil
}

// checkRedisCache 检查Redis缓存
func (s *SolveService) checkRedisCache(ctx *models.ProcessContext) bool {
	log.Printf("🔍 [CACHE-CHECK] 开始检查Redis缓存")
	log.Printf("🔍 [CACHE-CHECK] 使用HashKey: %s", ctx.SaveQuestion.HashKey)

	questions, err := s.cacheService.GetQuestionsByHashKey(ctx.SaveQuestion.HashKey)
	if err != nil || len(questions) == 0 {
		log.Printf("⚪ [CACHE-CHECK] Redis缓存未命中 - Error: %v, Questions数量: %d", err, len(questions))
		log.Printf("🔄 [CONTEXT] 缓存检查结果: 未命中，继续下一步")
		return false
	}

	// 缓存命中
	log.Printf("✅ [CACHE-CHECK] Redis缓存命中 - 找到%d个题目", len(questions))
	responseData, _ := json.Marshal(questions)
	ctx.SetSuccess("redis", responseData)

	// 记录ProcessContext更新
	log.Printf("🔄 [CONTEXT] ProcessContext已更新 - 缓存命中")
	log.Printf("🔄 [CONTEXT] Source: %s", ctx.Source)
	log.Printf("🔄 [CONTEXT] Status: %d", ctx.Status)
	log.Printf("🔄 [CONTEXT] Message: %s", ctx.Message)
	log.Printf("🔄 [CONTEXT] Response长度: %d bytes", len(ctx.Response))
	log.Printf("🔄 [CONTEXT] 处理完成: Redis缓存命中，直接返回结果")

	return true
}

// checkMySQLExactMatch 检查MySQL精确匹配
func (s *SolveService) checkMySQLExactMatch(ctx *models.ProcessContext) bool {
	log.Printf("🔍 [MYSQL-EXACT] 开始MySQL精确匹配")
	log.Printf("🔍 [MYSQL-EXACT] 使用HashKey: %s", ctx.SaveQuestion.HashKey)

	questions, err := s.questionService.GetQuestionsByHashKey(ctx.SaveQuestion.HashKey)
	if err != nil || len(questions) == 0 {
		log.Printf("⚪ [MYSQL-EXACT] MySQL精确匹配未命中 - Error: %v, Questions数量: %d", err, len(questions))
		log.Printf("🔄 [CONTEXT] 精确匹配结果: 未命中，继续模糊匹配")
		return false
	}

	// 转换为响应格式
	log.Printf("✅ [MYSQL-EXACT] MySQL精确匹配命中 - 找到%d个题目", len(questions))
	for i, q := range questions {
		log.Printf("✅ [MYSQL-EXACT] 题目%d - ID: %d, Type: %d, Content: %s", i+1, q.ID, q.Type, q.Content)
	}

	responses := s.parserService.ConvertToQuestionResponse(questions)
	responseData, _ := json.Marshal(responses)

	// 回写Redis缓存
	log.Printf("💾 [MYSQL-EXACT] 回写Redis缓存")
	s.cacheService.SetQuestionCache(ctx.SaveQuestion.HashKey, responses)

	ctx.SetSuccess("mysql", responseData)

	// 记录ProcessContext更新
	log.Printf("🔄 [CONTEXT] ProcessContext已更新 - MySQL精确匹配命中")
	log.Printf("🔄 [CONTEXT] Source: %s", ctx.Source)
	log.Printf("🔄 [CONTEXT] Status: %d", ctx.Status)
	log.Printf("🔄 [CONTEXT] Message: %s", ctx.Message)
	log.Printf("🔄 [CONTEXT] Response长度: %d bytes", len(ctx.Response))
	log.Printf("🔄 [CONTEXT] 处理完成: MySQL精确匹配命中，直接返回结果")

	return true
}

// checkMySQLFuzzyMatch 检查MySQL模糊匹配
func (s *SolveService) checkMySQLFuzzyMatch(ctx *models.ProcessContext) bool {
	log.Printf("🔍 [MYSQL-FUZZY] 开始MySQL模糊匹配")
	log.Printf("🔍 [MYSQL-FUZZY] 题目类型: %s", ctx.SaveQuestion.Type)
	log.Printf("🔍 [MYSQL-FUZZY] 清洗后内容: %s", ctx.SaveQuestion.CleanContent)
	log.Printf("🔍 [MYSQL-FUZZY] 题目长度: %d", ctx.SaveQuestion.QuestionLen)

	questions, err := s.matchService.MatchQuestion(ctx.SaveQuestion)
	if err != nil || len(questions) == 0 {
		log.Printf("⚪ [MYSQL-FUZZY] MySQL模糊匹配未命中 - Error: %v, Questions数量: %d", err, len(questions))
		log.Printf("🔄 [CONTEXT] 模糊匹配结果: 未命中，需要调用AI模型")
		return false
	}

	// 转换为响应格式
	log.Printf("✅ [MYSQL-FUZZY] MySQL模糊匹配命中 - 找到%d个相似题目", len(questions))
	for i, q := range questions {
		log.Printf("✅ [MYSQL-FUZZY] 相似题目%d - ID: %d, Type: %d, Content: %s", i+1, q.ID, q.Type, q.Content)
	}

	responses := s.parserService.ConvertToQuestionResponse(questions)
	responseData, _ := json.Marshal(responses)

	ctx.SetSuccess("match", responseData)

	// 记录ProcessContext更新
	log.Printf("🔄 [CONTEXT] ProcessContext已更新 - MySQL模糊匹配命中")
	log.Printf("🔄 [CONTEXT] Source: %s", ctx.Source)
	log.Printf("🔄 [CONTEXT] Status: %d", ctx.Status)
	log.Printf("🔄 [CONTEXT] Message: %s", ctx.Message)
	log.Printf("🔄 [CONTEXT] Response长度: %d bytes", len(ctx.Response))
	log.Printf("🔄 [CONTEXT] 处理完成: MySQL模糊匹配命中，直接返回结果")

	return true
}

// processSolve 处理Solve模型解答
func (s *SolveService) processSolve(ctx *models.ProcessContext) error {
	// 构建选项文本
	var optionsText string
	for key, value := range ctx.SaveQuestion.Options {
		if optionsText != "" {
			optionsText += " "
		}
		optionsText += fmt.Sprintf("%s:%s", key, value)
	}

	// 构建题目文本
	questionText := fmt.Sprintf("题目类型：%s\n题目内容：%s\n选项：%s",
		ctx.SaveQuestion.Type,
		ctx.SaveQuestion.Content,
		optionsText,
	)

	// 调用Solve模型
	response, err := s.modelService.CallSolveModel(s.cfg.Models.SolveModel, questionText)
	if err != nil {
		return fmt.Errorf("Solve模型调用失败: %w", err)
	}

	// 解析Solve响应
	solveData, err := s.parserService.ParseSolveResponse(response)
	if err != nil {
		return err
	}

	// 更新SaveQuestion
	ctx.SaveQuestion.Answer = solveData.Answer
	ctx.SaveQuestion.Analysis = solveData.Analysis

	return nil
}

// saveAndCache 保存题目并缓存
func (s *SolveService) saveAndCache(ctx *models.ProcessContext) error {
	// 保存到数据库
	question, err := s.questionService.SaveQuestion(ctx.SaveQuestion)
	if err != nil {
		return fmt.Errorf("保存题目失败: %w", err)
	}

	// 查询所有相同hash_key的题目
	allQuestions, err := s.questionService.GetQuestionsByHashKey(ctx.SaveQuestion.HashKey)
	if err != nil {
		return err
	}

	// 转换为响应格式
	responses := s.parserService.ConvertToQuestionResponse(allQuestions)
	responseData, _ := json.Marshal(responses)

	// 回写Redis缓存
	s.cacheService.SetQuestionCache(ctx.SaveQuestion.HashKey, responses)

	ctx.SetSuccess("ai", responseData)

	// 记录匹配的题目ID
	if len(allQuestions) > 0 {
		ctx.SaveQuestion.HashKey = fmt.Sprintf("%d", question.ID)
	}

	return nil
}
