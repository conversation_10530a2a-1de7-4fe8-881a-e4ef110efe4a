package database

import (
	"fmt"
	"solve-go-api/internal/config"
	"solve-go-api/internal/models"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// InitMySQL 初始化MySQL连接
func InitMySQL(cfg config.DatabaseConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		cfg.Username,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.Database,
		cfg.Charset,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MySQL: %w", err)
	}

	// 获取底层的sql.DB对象进行连接池配置
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)

	return db, nil
}

// Migrate 执行数据库迁移
func Migrate(db *gorm.DB) error {
	// 自动迁移所有模型
	err := db.AutoMigrate(
		&models.User{},
		&models.App{},
		&models.BalanceLog{},
		&models.SystemConfig{},
		&models.Model{},
		&models.QuestionBank{},
		&models.SolveLog{},
	)
	if err != nil {
		return fmt.Errorf("failed to migrate database: %w", err)
	}

	return nil
}

// InitSystemData 初始化系统数据
func InitSystemData(db *gorm.DB) error {
	// 检查是否已经初始化过
	var count int64
	db.Model(&models.User{}).Where("role IN ?", []string{"admin", "manager"}).Count(&count)
	if count > 0 {
		return nil // 已经初始化过
	}

	// 创建超级管理员
	admin := &models.User{
		Phone:    "***********",                                                  // 可以通过环境变量配置
		Password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // 123456的bcrypt哈希
		Role:     "admin",
		Nickname: "超级管理员",
		Balance:  0,
		IsActive: models.UserStatusActive, // 管理员默认激活
	}
	if err := db.Create(admin).Error; err != nil {
		return fmt.Errorf("failed to create admin user: %w", err)
	}

	// 创建题库管理员
	managers := []*models.User{
		{
			Phone:    "***********",
			Password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // 123456
			Role:     "manager",
			Nickname: "题库管理员1",
			Balance:  0,
			IsActive: models.UserStatusActive, // 管理员默认激活
		},
		{
			Phone:    "13800000003",
			Password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // 123456
			Role:     "manager",
			Nickname: "题库管理员2",
			Balance:  0,
			IsActive: models.UserStatusActive, // 管理员默认激活
		},
	}

	for _, manager := range managers {
		if err := db.Create(manager).Error; err != nil {
			return fmt.Errorf("failed to create manager user: %w", err)
		}
	}

	// 初始化默认模型配置
	defaultModels := []*models.Model{
		{
			ModelName:         "qwen-vl-plus",
			ModelURL:          "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
			ModelKey:          "", // 需要在环境变量中配置
			ModelType:         "OCR",
			RoleSystem:        "你是一个专业的题目识别助手，请识别图片中的题目内容，包括题目类型、题干和选项。",
			RoleUser:          "请识别这张图片中的题目，返回JSON格式：{\"qutext\":\"题干内容\",\"options\":{\"A\":\"选项A\",\"B\":\"选项B\"}}",
			Temperature:       0.1,
			TopP:              0.8,
			TopK:              50,
			RepetitionPenalty: 1.0,
			PresencePenalty:   0.0,
			ResponseFormat:    "json",
		},
		{
			ModelName:         "qwen-plus",
			ModelURL:          "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
			ModelKey:          "", // 需要在环境变量中配置
			ModelType:         "solve",
			RoleSystem:        "你是一个专业的题目解答助手，请根据题目内容给出正确答案和详细解析。",
			RoleUser:          "请解答这道题目，返回JSON格式：{\"answer\":\"答案\",\"analysis\":\"解析内容\"}",
			Temperature:       0.3,
			TopP:              0.9,
			TopK:              50,
			RepetitionPenalty: 1.0,
			PresencePenalty:   0.0,
			ResponseFormat:    "json",
		},
	}

	for _, model := range defaultModels {
		if err := db.Create(model).Error; err != nil {
			return fmt.Errorf("failed to create default model: %w", err)
		}
	}

	return nil
}
