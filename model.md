# 本文来讲解OCR与solve模型的相关业务；

### OCR模型业务介绍

    - OCR模型都来自于qwen大模型，其请求方式与请求结构体相同，仅参数不同，所以使用一套代码的方法即可
    - 调用模式 使用DashScope
    - 请求参数中存在两个user身份，其中一个是图片的，参数值为用户提交的图片url
    - 参考qwen-vl-plus的官方文档进行代码业务处理即可。
    - 其他参数全部数据库存储，参考下述字段建表，表名  hook_models

        id = 自增id；
        model_name = 模型名称；
        model_url = 模型请求地址；
        model_key = 模型的调用凭证KEY；
        model_type = 模型类型 //OCR = 搜题 或者 solve = 解题；
        role_system = system角色的Content；
        role_user = user角色的Content；
        temperature = 温度参数；
        top_p = TopP参数；
        top_k = TopK参数；
        repetition_penalty = 重复惩罚；
        presence_penalty = 存在惩罚；
        response_format = 返回格式；
        created_at = 创建时间；
        updated_at = 更新时间；                


### Solve模型业务介绍

    - Solve模型分为qwen系列模型与deepseek模型，其参数只有一个字段请求的参数命名不同但功能一致，所以可以共用一个数据表。
    - 请求体存在差异，所以需要设置两套方法来处理，
    - 请求参数中user的role中需要包含question_text信息。
    - deepseek模型参考deepseek-chat的官方文档进行代码业务处理即可。
    - qwen模型参考qwen-plus的官方文档进行代码业务处理即可。


### 不同模型响应格式不同，但最终都会使用统一的数据解析来进行处理，所以只有响应结构存在差异，数据解析都是使用统一流程。需要针对不同的模型处理不同的响应结构逻辑。